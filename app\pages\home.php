<?php
// 首页

// 页面标题
$title = '首页';

// 页面文件名（不含.php扩展名）
$pageName = 'home';

// 页面内容
ob_start();
?>

<div class="jumbotron bg-primary text-white rounded p-5 mb-4">
    <div class="container">
        <h1 class="display-4">欢迎使用 AI PHP 框架</h1>
        <p class="lead">这是一个功能强大且易于使用的PHP框架，旨在帮助您快速开发Web应用程序。</p>
        <hr class="my-4 bg-white">
        <p>了解更多关于我们的框架的信息。</p>
        <a class="btn btn-light btn-lg" href="#" role="button">了解更多</a>
    </div>
</div>

<div class="container">
    <div class="row">
        <div class="col-md-4">
            <div class="card mb-4 shadow-sm">
                <div class="card-body">
                    <h5 class="card-title">简单易用</h5>
                    <p class="card-text">AI PHP 框架提供了简洁的API和清晰的文档，让您可以快速上手并开始开发。</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card mb-4 shadow-sm">
                <div class="card-body">
                    <h5 class="card-title">功能强大</h5>
                    <p class="card-text">内置了路由、数据库操作、模板引擎等丰富的功能，满足各种开发需求。</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card mb-4 shadow-sm">
                <div class="card-body">
                    <h5 class="card-title">易于扩展</h5>
                    <p class="card-text">模块化设计使得框架易于扩展，您可以根据需要添加自定义功能。</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col text-center py-5">
            <h2>开始您的项目</h2>
            <p class="lead">准备好开始使用AI PHP框架创建您的下一个项目了吗？</p>
            <a class="btn btn-primary btn-lg" href="/users" role="button">查看用户列表</a>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();

// 引入布局模板
require_once ROOT_PATH . '/app/layouts/main.php';