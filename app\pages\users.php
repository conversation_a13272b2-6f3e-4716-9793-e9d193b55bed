<?php
// 用户列表页面

// 检查是否有分页参数
$page = isset($page) ? (int)$page : 1;
if ($page < 1) $page = 1;

// 页面标题
$title = '用户列表';

// 页面文件名（不含.php扩展名）
$pageName = 'users';

// 设置每页显示记录数
$perPage = 20;

// 计算偏移量
$offset = ($page - 1) * $perPage;

// 获取用户数据（带分页）
$db = \Database\Connection::getInstance();
$query = new \Database\QueryBuilder();
$users = $query->table('users')
               ->orderBy('id', 'ASC')
               ->limit($perPage)
               ->offset($offset)
               ->get();

// 获取总用户数
$countResult = $db->query("SELECT COUNT(*) as count FROM users");
$totalUsers = isset($countResult[0]['count']) ? $countResult[0]['count'] : 0;

// 计算总页数
$totalPages = ceil($totalUsers / $perPage);

// 确保当前页码不超过总页数
if ($page > $totalPages && $totalPages > 0) {
    $page = $totalPages;
}

// 页面内容
ob_start();
?>

<div class="container">
    <!-- 页面标题和新建用户按钮 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">用户列表</h2>
        <a href="/users/create" class="btn btn-primary">新建用户</a>
    </div>
    
    <!-- 用户表格 -->
    <div class="table-responsive">
        <table class="table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>姓名</th>
                    <th>邮箱</th>
                    <th>电话</th>
                    <th>地址</th>
                    <th>创建时间</th>
                    <th>更新时间</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($users as $user): ?>
                <tr>
                    <td><?php echo htmlspecialchars($user['id']); ?></td>
                    <td><?php echo htmlspecialchars($user['name']); ?></td>
                    <td><?php echo htmlspecialchars($user['email']); ?></td>
                    <td><?php echo htmlspecialchars($user['phone']); ?></td>
                    <td><?php echo htmlspecialchars($user['address']); ?></td>
                    <td><?php echo htmlspecialchars($user['created_at']); ?></td>
                    <td><?php echo htmlspecialchars($user['updated_at']); ?></td>
                    <td><a href="/users/<?php echo htmlspecialchars($user['id']); ?>">查看</a></td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>

    <!-- 分页 -->
    <nav aria-label="用户列表分页" class="mt-4">
        <ul class="pagination justify-content-center">
            <?php if ($page > 1): ?>
            <li class="page-item">
                <a class="page-link" href="/users/page/1">首页</a>
            </li>
            <li class="page-item">
                <a class="page-link" href="/users/page/<?php echo $page - 1; ?>">上一页</a>
            </li>
            <?php endif; ?>
            
            <?php
            // 显示当前页码周围的页码
            $start = max(1, $page - 2);
            $end = min($totalPages, $page + 2);
            
            if ($start > 1): ?>
            <li class="page-item disabled">
                <span class="page-link">...</span>
            </li>
            <?php endif; ?>
            
            <?php for ($i = $start; $i <= $end; $i++): ?>
            <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                <a class="page-link" href="/users/page/<?php echo $i; ?>">
                    <?php echo $i; ?>
                </a>
            </li>
            <?php endfor; ?>
            
            <?php if ($end < $totalPages): ?>
            <li class="page-item disabled">
                <span class="page-link">...</span>
            </li>
            <?php endif; ?>
            
            <?php if ($page < $totalPages): ?>
            <li class="page-item">
                <a class="page-link" href="/users/page/<?php echo $page + 1; ?>">下一页</a>
            </li>
            <li class="page-item">
                <a class="page-link" href="/users/page/<?php echo $totalPages; ?>">末页</a>
            </li>
            <?php endif; ?>
        </ul>
    </nav>
    
    <div class="text-center mt-3">
        <p class="text-muted">
            显示第 <?php echo ($offset + 1); ?> 至 <?php echo min($offset + $perPage, $totalUsers); ?> 条，
            共 <?php echo $totalUsers; ?> 条记录
        </p>
    </div>
</div>

<?php
$content = ob_get_clean();

// 引入布局模板
require_once ROOT_PATH . '/app/layouts/main.php';