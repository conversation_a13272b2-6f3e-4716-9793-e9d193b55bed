<?php
// 路由调试脚本

require_once __DIR__ . '/core/bootstrap.php';

// 获取所有注册的路由
$routes = \Core\Routes\RouteRegistrar::getRoutes();

echo "<h1>注册的路由</h1>";
echo "<pre>";
print_r($routes);
echo "</pre>";

// 测试路由解析
$testUris = [
    '/',
    '/users',
    '/reader',
    '/csrf_test'
];

foreach ($testUris as $uri) {
    echo "<h2>测试 URI: $uri</h2>";
    $result = \Core\Routes\RouteResolver::resolve($uri, 'GET');
    if ($result) {
        echo "<p>匹配结果:</p>";
        echo "<pre>";
        print_r($result);
        echo "</pre>";
    } else {
        echo "<p style='color: red;'>未匹配到路由</p>";
    }
}