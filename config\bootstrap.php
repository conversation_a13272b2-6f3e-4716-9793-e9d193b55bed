<?php

// 引导文件 - 负责初始化应用程序

// 1. 设置错误报告级别
error_reporting(E_ALL);
ini_set('display_errors', '1');

// 2. 定义核心常量
// 所有目录常量已在入口文件中定义

// 3. 注册自动加载函数
require_once CORE_PATH . '/own-library/autoloader/autoloader.php';

use Core\OwnLibrary\Autoloader\Autoloader;

$autoloader = new Autoloader();
$autoloader->addNamespace('Core\OwnLibrary', CORE_PATH . '/own-library');
$autoloader->addNamespace('Core\OtherLibrary', CORE_PATH . '/other-library');
$autoloader->register();

// 4. 加载路由配置
$routes = require CONFIG_PATH . '/routes.php';

// 5. 初始化并运行路由
use Core\OwnLibrary\Routing\Router;

// 调试输出，查看请求信息
// echo "<pre style='background-color:#f0f8ff; padding:10px; margin:10px 0; border:1px solid #ddd;'>";
// echo "请求信息:\n";
// echo "REQUEST_URI: " . ($_SERVER['REQUEST_URI'] ?? 'N/A') . "\n";
// echo "REQUEST_METHOD: " . ($_SERVER['REQUEST_METHOD'] ?? 'N/A') . "\n";
// echo "QUERY_STRING: " . ($_SERVER['QUERY_STRING'] ?? 'N/A') . "\n";
// echo "</pre>";

$router = new Router($routes);
$routeResult = $router->match();
// 调试输出，查看路由结果
// echo "<pre style='background-color:#f5f5f5; padding:10px; margin:10px 0; border:1px solid #ddd;'>";
// echo "路由解析结果:\n";
// print_r($routeResult);
// echo "</pre>";

// 6. 实例化页面加载器并加载页面
use Core\OwnLibrary\PageLoader\PageLoader;

$pageLoader = new PageLoader();
$routePath = $routeResult['path'];
$routeParams = $routeResult['params'];
$pageLoader->loadPage($routePath, $routeParams);