<?php

namespace Filters;

/**
 * 数据过滤和验证类
 * 
 * 提供常用的数据验证功能，包括：
 * - ID参数验证
 * - 邮箱地址验证
 * - 手机号码验证
 * - 字符串长度验证
 * - URL格式验证
 * - IP地址验证
 * - 日期格式验证
 */
class Filter
{
    /**
     * 验证ID参数必须是大于0的整数
     * 
     * @param mixed $id 待验证的ID参数
     * @return bool 验证结果
     */
    public static function validateId($id): bool
    {
        return is_numeric($id) && intval($id) > 0 && intval($id) == $id;
    }

    /**
     * 验证邮箱地址格式
     * 
     * @param string $email 待验证的邮箱地址
     * @return bool 验证结果
     */
    public static function validateEmail($email): bool
    {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }

    /**
     * 验证手机号码格式（中国手机号）
     * 
     * @param string $phone 待验证的手机号码
     * @return bool 验证结果
     */
    public static function validatePhone($phone): bool
    {
        return preg_match('/^1[3-9]\d{9}$/', $phone) === 1;
    }

    /**
     * 验证字符串长度
     * 
     * @param string $str 待验证的字符串
     * @param int $min 最小长度
     * @param int $max 最大长度
     * @return bool 验证结果
     */
    public static function validateStringLength($str, int $min, int $max): bool
    {
        $length = mb_strlen($str, 'UTF-8');
        return $length >= $min && $length <= $max;
    }

    /**
     * 验证URL格式
     * 
     * @param string $url 待验证的URL
     * @return bool 验证结果
     */
    public static function validateUrl($url): bool
    {
        return filter_var($url, FILTER_VALIDATE_URL) !== false;
    }

    /**
     * 验证IP地址格式
     * 
     * @param string $ip 待验证的IP地址
     * @return bool 验证结果
     */
    public static function validateIp($ip): bool
    {
        return filter_var($ip, FILTER_VALIDATE_IP) !== false;
    }

    /**
     * 验证日期格式
     * 
     * @param string $date 待验证的日期
     * @param string $format 日期格式，默认为'Y-m-d'
     * @return bool 验证结果
     */
    public static function validateDate($date, string $format = 'Y-m-d'): bool
    {
        $d = \DateTime::createFromFormat($format, $date);
        return $d && $d->format($format) === $date;
    }
}