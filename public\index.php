<?php
// 入口文件

// 定义项目根目录常量
define('ROOT_PATH', dirname(__DIR__));

// 引入路由解析器
require_once ROOT_PATH . '/core/routes/Router.php';

// 获取当前请求的URI
$uri = Router::getCurrentUri();

// 解析路由获取对应的页面文件
$page = Router::resolve($uri);

// 引入对应的页面文件
if (file_exists(ROOT_PATH . '/core/pages/' . $page)) {
    require_once ROOT_PATH . '/core/pages/' . $page;
} else {
    // 如果页面文件不存在，显示404错误
    http_response_code(404);
    echo "404 - 页面未找到";
}