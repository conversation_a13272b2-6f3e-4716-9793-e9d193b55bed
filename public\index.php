<?php
// 入口文件

// 启动会话
session_start();

// 引入引导文件（包含所有必要的初始化）
require_once dirname(__DIR__) . '/core/bootstrap.php';

// 获取请求URI和方法
$requestUri = $_SERVER['REQUEST_URI'] ?? '/';
$requestMethod = $_SERVER['REQUEST_METHOD'] ?? 'GET';

// 解析路由
$routeInfo = \Core\Routes\RouteResolver::resolve($requestUri, $requestMethod);

if ($routeInfo) {
    // 提取处理器和参数
    $handler = $routeInfo['handler'];
    $params = $routeInfo['params'];
    
    // 将参数设置为全局变量，以便在页面中使用
    foreach ($params as $key => $value) {
        $$key = $value;
    }
    
    // 检查页面文件是否存在
    $pagePath = ROOT_PATH . '/app/pages/' . $handler;
    if (file_exists($pagePath)) {
        require_once $pagePath;
    } else {
        // 页面不存在，显示404页面
        require_once ROOT_PATH . '/app/pages/404.php';
    }
} else {
    // 未匹配到路由，显示404页面
    require_once ROOT_PATH . '/app/pages/404.php';
}