# AiPHP 开发提示词模板

## 1. 核心功能开发类

### 1.1 创建核心功能
``` 
请根据以下规范创建核心功能：

1. 基本信息：
- 功能名称：{功能名称，如果是中文会自动翻译为英文作为类名}
- 所属模块：{模块名称}
- 功能描述：{具体的功能描述}

2. 目录结构规范：
- 在 core/ 目录下根据模块名创建对应文件夹
- 使用驼峰命名法命名类文件
- 遵循 PSR-4 自动加载规范

3. 类文件要求：
- 命名空间：Core\{ModuleName}
- 类名：使用大驼峰命名法（如：UserManager, FileHandler）
- 属性和方法：使用小驼峰命名法
- 完整的 PHPDoc 注释（便于AI理解和处理）
- 单一职责原则

4. 依赖处理：
- 不在类中直接引入其他类文件
- 所有类的自动加载配置统一在 core/bootstrap.php 中处理
- 使用依赖注入方式处理类之间的关系

5. 注释规范：
```php
/**
 * 类的主要功能描述
 * 
 * @category    Core
 * @package     {ModuleName}
 * <AUTHOR> Assistant
 * @version     1.0.0
 * 
 * @property    type $propertyName 属性描述
 * @method      type methodName(type $param) 方法描述
 * 
 * @example     示例代码或使用说明
 * 
 * Class {ClassName}
 */
```

6. 测试说明：
- 开发完成后不要自动运行测试
- 提供使用示例代码
- 说明预期的输入输出
```

## 2. 页面开发类

### 1.1 创建新页面
``` 
请创建一个名为 {page_name}.php 的页面文件，包含以下功能：
- 使用 main.php 布局模板，正确实现输出缓冲和布局引入
- 页面标题为 "{页面标题}"
- 包含基本的 HTML 结构
- 添加适当的 CSS 类名以便样式化
```

### 1.2 页面开发规范
``` 
请确保在创建页面时遵循以下规范：
- 在页面文件中正确使用布局模板
- 使用 ob_start() 开始输出缓冲
- 将页面内容放在缓冲区中
- 使用 ob_get_clean() 获取页面内容并存储在 $content 变量中
- 通过 include_once 引入布局文件 app/layouts/main.php
- 确保页面变量（如 $title, $pageName）正确定义
- 创建对应的CSS和JS文件
- 在路由配置中添加相应路由
- 在导航菜单中添加页面链接
```

### 1.3 删除页面
``` 
请帮我彻底删除名为 {page_name} 的页面，确保：
- 删除 app/pages/{page_name}.php 页面文件
- 删除 public/static/css/{page_name}.css 样式文件（如存在）
- 删除 public/static/js/{page_name}.js 脚本文件（如存在）
- 从 core/routes/web.php 路由配置中移除相关路由
- 从 app/layouts/main.php 导航菜单中移除相关链接
- 检查并更新其他可能引用该页面的文件
- 确保删除后不会出现404错误或其他问题
```

### 1.4 清理无用的CSS和JS文件
``` 
请根据页面名称和布局名称清理无用的CSS和JS文件，确保资源文件与页面和布局一一对应：
- 检查 app/pages/ 目录下的所有页面文件
- 检查 app/layouts/ 目录下的所有布局文件
- 识别与页面名称对应的CSS和JS文件（public/static/css/{page_name}.css 和 public/static/js/{page_name}.js）
- 识别与布局名称对应的CSS文件（public/static/css/{layout_name}.css）
- 删除没有对应页面或布局的孤立CSS和JS文件
- 检查页面文件中$pageName变量的设置是否正确
- 验证清理后页面和布局的样式及功能是否正常
```

### 1.5 创建新建表单页面（Create Form）
```
请创建一个新建表单页面，用于创建新记录，包含以下功能：

1. 基本信息：
- 页面名称：{page_name}（例如：users_create）
- 表单用途：{表单的具体用途描述，例如：创建新用户}
- 包含字段：{字段列表，每个字段包括名称、类型、是否必填等信息}

2. 页面要求：
- 使用 main.php 布局模板
- 页面标题为 "{页面标题}"
- 创建独立的CSS和JS文件
- 表单字段需要有适当的placeholder和提示信息
- 实现表单验证（前端和后端）
- 提供用户友好的错误和成功消息提示
- 添加确认操作的模态框（如删除、离开页面等）

3. 功能要求：
- 表单提交处理
- 数据验证（必填字段、格式验证等）
- 错误处理和消息显示
- 成功处理和消息显示
- 返回/取消操作确认（使用模态框）

4. 样式要求：
- 创建独立的CSS文件
- 使用一致的设计风格
- 响应式设计
- 包含表单、按钮、消息提示等样式

5. JavaScript要求：
- 创建独立的JS文件
- 表单验证
- 用户交互增强
- 自定义模态框
- 页面离开确认等

6. 路由要求：
- 在 core/routes/web.php 中添加GET和POST路由
- GET路由用于显示表单
- POST路由用于处理表单提交

7. 导航要求：
- 通常通过列表页面或其他入口访问，不在主导航中显示

8. 表单布局要求：
- 提交按钮应该放在表单底部
- 返回/取消按钮也应该放在表单底部
- 表单字段应包含适当的placeholder提示文字
- 点击返回/取消按钮时应弹出确认模态框

9. 创建新建表单页面的详细过程：
- 创建页面文件 (app/pages/{page_name}.php)
- 实现表单处理逻辑（包括GET显示和POST处理）
- 创建对应的CSS样式文件 (public/static/css/{page_name}.css)
- 创建对应的JavaScript文件 (public/static/js/{page_name}.js)
- 在路由配置文件中添加路由规则 (core/routes/web.php)

10. 注意事项：
- 确保模态框放置在表单外部，避免表单提交问题
- 为模态框中的按钮添加 preventDefault() 调用，防止意外触发表单提交
- 正确处理表单验证和错误消息显示
- 确保前后端验证一致
- 使用适当的安全措施（如CSRF保护、输入过滤等）
- 保持一致的用户体验和设计风格
- 确保页面在各种设备上的响应式表现
- 新建表单与后续可能的编辑表单区分开，编辑表单通常需要加载现有数据
```

## 2. 布局开发类

### 2.1 创建新布局

在创建新的布局文件之前，请提供以下信息：

1. **布局名称**：请提供布局的中文名称和英文名称
2. **主题风格**：请描述布局的主题风格（如：科技感、简约、复古等）
3. **主色调**：请指定布局的主要颜色（如：红色、蓝色、绿色等）
4. **特殊功能**：是否需要特殊功能（如：响应式设计、动画效果、暗黑模式等）

#### 布局文件创建步骤

1. 创建布局文件：
- 文件路径：`app/layouts/{layout_name}.php`
- 文件命名：使用英文小写字母和下划线命名
- 基本结构应包含：
  ```php
  <!DOCTYPE html>
  <html lang="zh-CN">
  <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title><?php echo isset($title) ? htmlspecialchars($title) : '默认标题'; ?></title>
      <link rel="stylesheet" href="/static/css/{layout_name}.css">
      <?php if (isset($pageName)): ?>
          <link rel="stylesheet" href="/static/css/<?php echo htmlspecialchars($pageName); ?>.css">
      <?php endif; ?>
  </head>
  <body>
      <!-- 布局内容 -->
      <main>
          <?php echo isset($content) ? $content : ''; ?>
      </main>
  </body>
  </html>
  ```

2. 创建样式文件：
- 文件路径：`public/static/css/{layout_name}.css`
- 样式文件应包含：
  - 基本的页面样式
  - 响应式设计
  - 主题颜色定义
  - 特殊效果实现

### 2.2 布局设计规范

1. HTML结构规范：
- 使用语义化标签
- 使用语义化标签
- 保持结构清晰简洁
- 确保良好的可访问性

2. CSS样式规范：
- 使用类名命名规范（如：BEM命名法）
- 合理使用CSS变量定义主题颜色
- 确保响应式设计适配不同设备

3. PHP代码规范：
- 正确使用输出缓冲获取页面内容
- 安全处理变量输出（使用htmlspecialchars）
- 遵循模板系统规范

### 2.3 布局使用说明

1. 在页面中使用新布局：
```php
// 设置页面标题和名称
$title = "页面标题";
$pageName = "页面名称";

// 开始输出缓冲
ob_start();

// 页面内容...

// 获取页面内容并引入布局
$content = ob_get_clean();
require_once  __DIR__ . '/../layouts/{layout_name}.php';
```

2. 更换页面布局：
```php
// 将
require_once  __DIR__ . '/../layouts/main.php';
// 更换为
require_once  __DIR__ . '/../layouts/{new_layout_name}.php';