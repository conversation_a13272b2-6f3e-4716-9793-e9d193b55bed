/* 主题颜色变量定义 */
:root {
    --tech-green-primary: #00ff9d;
    --tech-green-secondary: #00cc7e;
    --tech-green-dark: #008552;
    --tech-green-light: #7affcd;
    --tech-bg: #1a1a1a;
    --tech-text: #e0e0e0;
    --tech-border: rgba(0, 255, 157, 0.3);
}

/* 基础样式 */
body {
    margin: 0;
    padding: 0;
    font-family: 'Segoe UI', Arial, sans-serif;
    background-color: var(--tech-bg);
    color: var(--tech-text);
    line-height: 1.6;
}

/* 科技感header */
.tech-header {
    background: linear-gradient(45deg, var(--tech-green-dark), var(--tech-green-secondary));
    padding: 1rem 2rem;
    position: relative;
    overflow: hidden;
    border-bottom: 2px solid var(--tech-border);
}

.tech-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--tech-green-primary);
    box-shadow: 0 0 15px var(--tech-green-primary);
}

.tech-header h1 {
    margin: 0;
    font-size: 1.8rem;
    text-transform: uppercase;
    letter-spacing: 2px;
}

/* 科技感导航栏 */
.tech-nav {
    background-color: rgba(0, 133, 82, 0.2);
    padding: 1rem 2rem;
    border-bottom: 1px solid var(--tech-border);
    display: flex;
    justify-content: flex-end;
    align-items: center;
}

.tech-nav a {
    color: var(--tech-green-primary);
    text-decoration: none;
    padding: 0.5rem 1.5rem;
    margin: 0 0.5rem;
    border: 1px solid var(--tech-border);
    border-radius: 4px;
    transition: all 0.3s ease;
}

.tech-nav a:hover {
    background-color: var(--tech-green-primary);
    color: var(--tech-bg);
    box-shadow: 0 0 10px var(--tech-green-primary);
}

/* 主内容区域 */
.tech-main {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 2rem;
}

/* 科技感页脚 */
.tech-footer {
    background: linear-gradient(45deg, var(--tech-green-dark), var(--tech-green-secondary));
    color: var(--tech-text);
    text-align: center;
    padding: 1rem;
    position: relative;
    border-top: 2px solid var(--tech-border);
}

.tech-footer::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--tech-green-primary);
    box-shadow: 0 0 15px var(--tech-green-primary);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .tech-nav {
        padding: 0.5rem;
    }

    .tech-nav a {
        display: block;
        margin: 0.5rem 0;
        text-align: center;
    }

    .tech-main {
        padding: 0 1rem;
        margin: 1rem auto;
    }
}

/* 科技感动画效果 */
@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(0, 255, 157, 0.4); }
    70% { box-shadow: 0 0 0 10px rgba(0, 255, 157, 0); }
    100% { box-shadow: 0 0 0 0 rgba(0, 255, 157, 0); }
}
