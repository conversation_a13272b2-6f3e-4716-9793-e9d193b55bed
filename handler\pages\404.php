<?php
// 获取当前请求的URI
$requestedUri = $_SERVER['REQUEST_URI'] ?? '';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>404 - 页面未找到</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            color: #333;
        }
        .container {
            text-align: center;
            background-color: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            max-width: 500px;
        }
        h1 {
            font-size: 3rem;
            color: #e74c3c;
            margin-bottom: 1rem;
        }
        p {
            font-size: 1.2rem;
            line-height: 1.6;
            margin: 0.5rem 0;
        }
        .error-code {
            font-size: 5rem;
            font-weight: bold;
            color: #bdc3c7;
            margin-bottom: 1rem;
        }
        .requested-uri {
            background-color: #f8f9fa;
            padding: 0.5rem;
            border-radius: 4px;
            font-family: monospace;
            word-break: break-all;
        }
        a {
            color: #3498db;
            text-decoration: none;
            font-weight: bold;
        }
        a:hover {
            text-decoration: underline;
        }
        .button-group {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-top: 1.5rem;
            flex-wrap: wrap;
        }
        .btn {
            display: inline-block;
            padding: 0.8rem 1.5rem;
            border-radius: 4px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s;
            cursor: pointer;
            border: none;
            font-size: 1rem;
        }
        .btn-primary {
            background-color: #3498db;
            color: white;
        }
        .btn-primary:hover {
            background-color: #2980b9;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        .btn-secondary {
            background-color: #95a5a6;
            color: white;
        }
        .btn-secondary:hover {
            background-color: #7f8c8d;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        .btn-back {
            background-color: #2ecc71;
            color: white;
        }
        .btn-back:hover {
            background-color: #27ae60;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        .suggestion {
            margin-top: 1.5rem;
            padding: 1rem;
            background-color: #f8f9fa;
            border-radius: 4px;
            text-align: left;
        }
        .suggestion h3 {
            margin-top: 0;
            color: #2c3e50;
        }
        .suggestion ul {
            text-align: left;
            padding-left: 1.5rem;
        }
        .suggestion li {
            margin-bottom: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="error-code">404</div>
        <h1>页面未找到</h1>
        <p>抱歉，您请求的页面不存在。</p>
        <p>请检查您输入的URL是否正确。</p>
        
        <?php if (!empty($requestedUri)): ?>
            <p>请求的地址: <span class="requested-uri"><?php echo htmlspecialchars($requestedUri); ?></span></p>
        <?php endif; ?>
        
        <div class="button-group">
            <button class="btn btn-back" onclick="goBack()">← 返回上一页</button>
            <a href="/" class="btn btn-primary">🏠 返回首页</a>
        </div>
        
        <div class="suggestion">
            <h3>可能的解决方案:</h3>
            <ul>
                <li>检查URL地址是否正确</li>
                <li>返回首页并重新导航到目标页面</li>
                <li>如果问题持续存在，请联系网站管理员</li>
            </ul>
        </div>
    </div>
    
    <script>
        function goBack() {
            // 尝试使用浏览器历史记录返回上一页
            if (window.history.length > 1) {
                window.history.back();
            } else {
                // 如果没有历史记录，则返回首页
                window.location.href = '/';
            }
        }
        
        // 添加键盘事件支持，按ESC键返回上一页
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                goBack();
            }
        });
    </script>
</body>
</html>