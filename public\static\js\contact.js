// 联系页面专用JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // 添加联系项动画效果
    const contactItems = document.querySelectorAll('.contact-item');
    
    contactItems.forEach(function(item, index) {
        // 添加延迟动画效果
        item.style.opacity = '0';
        item.style.transform = 'translateY(20px)';
        item.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
        
        setTimeout(function() {
            item.style.opacity = '1';
            item.style.transform = 'translateY(0)';
        }, 200 * index);
    });
    
    // 地图加载效果
    const mapContainer = document.querySelector('.map-placeholder');
    if (mapContainer) {
        const mapPlaceholder = mapContainer.querySelector('p');
        const iframe = mapContainer.querySelector('iframe');
        
        // 模拟地图加载
        setTimeout(function() {
            if (mapPlaceholder && iframe) {
                mapPlaceholder.textContent = '地图加载中...';
                
                setTimeout(function() {
                    mapPlaceholder.style.display = 'none';
                    iframe.style.display = 'block';
                }, 1000);
            }
        }, 1500);
    }
});