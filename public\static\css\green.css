:root {
    --primary-color: #4CAF50;
    --primary-dark: #388E3C;
    --primary-light: #C8E6C9;
    --accent-color: #8BC34A;
    --text-primary: #212121;
    --text-secondary: #757575;
    --background-color: #F5F5F5;
}

/* 基本样式 */
body {
    margin: 0;
    padding: 0;
    font-family: Arial, sans-serif;
    color: var(--text-primary);
    background-color: var(--background-color);
    line-height: 1.6;
}

/* 头部样式 */
.header {
    background-color: var(--primary-color);
    color: white;
    padding: 1rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header h1 {
    margin: 0;
    font-size: 1.5rem;
}

/* 导航栏样式 */
.nav {
    background-color: var(--primary-dark);
    padding: 0.5rem 2rem;
    display: flex;
    justify-content: flex-end;
    align-items: center;
}

.nav a {
    color: white;
    text-decoration: none;
    padding: 0.5rem 1rem;
    margin: 0 0.5rem;
    border-radius: 3px;
    transition: background-color 0.3s;
}

.nav a:hover {
    background-color: rgba(255,255,255,0.1);
}

/* 主要内容区域 */
.main-content {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 1rem;
    min-height: calc(100vh - 200px);
}

/* 页脚样式 */
.footer {
    background-color: var(--primary-dark);
    color: white;
    text-align: center;
    padding: 1rem;
    margin-top: 2rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-content {
        margin: 1rem auto;
    }
    
    .nav {
        text-align: center;
    }
    
    .nav a {
        display: block;
        margin: 0.5rem 0;
    }
}
