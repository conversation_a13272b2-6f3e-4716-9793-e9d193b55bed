<?php
// 路由解析器类

class Router
{
    /**
     * 解析当前请求的路由
     * 
     * @param string $uri 当前请求的URI
     * @return string 对应的页面文件
     */
    public static function resolve($uri)
    {
        // 加载路由配置
        $routes = require_once __DIR__ . '/web.php';
        
        // 标准化URI（移除查询参数）
        $uri = parse_url($uri, PHP_URL_PATH);
        
        // 直接匹配路由
        if (isset($routes[$uri])) {
            return $routes[$uri];
        }
        
        // 如果没有匹配的路由，返回404页面
        return '404.php';
    }
    
    /**
     * 获取当前请求的URI
     * 
     * @return string
     */
    public static function getCurrentUri()
    {
        $uri = $_SERVER['REQUEST_URI'] ?? '/';
        return $uri;
    }
}