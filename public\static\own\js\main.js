// 主JavaScript文件

document.addEventListener('DOMContentLoaded', function() {
    // 添加一些基本的交互功能
    
    // 移动端导航菜单切换
    const mobileMenuToggle = document.createElement('button');
    mobileMenuToggle.classList.add('mobile-menu-toggle');
    mobileMenuToggle.innerHTML = '☰';
    mobileMenuToggle.setAttribute('aria-label', '切换导航菜单');
    
    const siteNav = document.querySelector('.site-nav');
    if (siteNav) {
        // 检查是否是移动端
        if (window.innerWidth <= 768) {
            siteNav.parentNode.insertBefore(mobileMenuToggle, siteNav);
            siteNav.classList.add('mobile-hidden');
        }
        
        mobileMenuToggle.addEventListener('click', function() {
            siteNav.classList.toggle('mobile-hidden');
        });
    }
    
    // 表单提交处理
    const forms = document.querySelectorAll('form');
    forms.forEach(function(form) {
        form.addEventListener('submit', function(e) {
            // 可以在这里添加表单验证或其他处理逻辑
            console.log('表单提交:', form);
        });
    });
    
    // 为所有链接添加平滑滚动效果
    const links = document.querySelectorAll('a[href^="#"]');
    links.forEach(function(link) {
        link.addEventListener('click', function(e) {
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                e.preventDefault();
                target.scrollIntoView({
                    behavior: 'smooth'
                });
            }
        });
    });
});

// 窗口大小改变时的处理
window.addEventListener('resize', function() {
    const siteNav = document.querySelector('.site-nav');
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    
    if (siteNav && mobileMenuToggle) {
        if (window.innerWidth <= 768) {
            if (!mobileMenuToggle.parentNode) {
                siteNav.parentNode.insertBefore(mobileMenuToggle, siteNav);
            }
            siteNav.classList.add('mobile-hidden');
        } else {
            if (mobileMenuToggle.parentNode) {
                mobileMenuToggle.parentNode.removeChild(mobileMenuToggle);
            }
            siteNav.classList.remove('mobile-hidden');
        }
    }
});