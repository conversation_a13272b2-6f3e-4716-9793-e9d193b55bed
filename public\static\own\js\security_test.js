// 安全测试页面的JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // 为测试链接添加点击事件
    const testLinks = document.querySelectorAll('.test-list a');
    testLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // 记录点击的测试链接
            console.log('点击测试链接:', this.href);
            
            // 为危险链接添加确认对话框
            if (this.classList.contains('danger')) {
                if (!confirm('这个链接用于测试安全防护机制，可能会被阻止。是否继续？')) {
                    e.preventDefault();
                }
            }
        });
    });
    
    // 高亮显示当前页面信息
    const currentUri = window.location.pathname + window.location.search;
    const testLinksArray = Array.from(testLinks);
    const currentLink = testLinksArray.find(link => {
        // 处理特殊字符编码
        const linkHref = new URL(link.href, window.location.origin).pathname;
        const currentPath = new URL(currentUri, window.location.origin).pathname;
        return linkHref === currentPath;
    });
    
    if (currentLink) {
        currentLink.style.fontWeight = 'bold';
        currentLink.style.color = '#28a745';
    }
    
    // 添加页面加载动画
    const container = document.querySelector('.security-test-container');
    if (container) {
        container.style.opacity = '0';
        container.style.transition = 'opacity 0.5s ease-in-out';
        
        setTimeout(() => {
            container.style.opacity = '1';
        }, 100);
    }
    
    // 为测试类别添加展开/折叠功能
    const categoryHeaders = document.querySelectorAll('.test-category h2');
    categoryHeaders.forEach(header => {
        header.style.cursor = 'pointer';
        header.addEventListener('click', function() {
            const category = this.parentElement;
            const list = category.querySelector('.test-list');
            if (list) {
                if (list.style.display === 'none') {
                    list.style.display = 'block';
                    this.style.color = '#2d6a4f';
                } else {
                    list.style.display = 'none';
                    this.style.color = '#6c757d';
                }
            }
        });
    });
    
    // 添加测试结果高亮
    const uriElement = document.querySelector('.test-info li:first-child span');
    if (uriElement) {
        const currentUri = window.location.pathname;
        if (currentUri.includes('security-test')) {
            uriElement.parentElement.style.backgroundColor = '#d4edda';
            uriElement.parentElement.style.padding = '10px';
            uriElement.parentElement.style.borderRadius = '4px';
        }
    }
});