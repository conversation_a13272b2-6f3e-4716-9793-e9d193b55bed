<?php
// 网站首页

// 页面标题
$title = '网站首页';

// 页面文件名（不含.php扩展名）
$pageName = 'home';

// 页面内容
ob_start();
?>
<div class="hero">
    <h1>欢迎来到AI PHP网站</h1>
    <p>一个现代化、高效的PHP开发框架</p>
    <button class="cta-button">开始使用</button>
</div>

<div class="features">
    <div class="feature">
        <h2>快速开发</h2>
        <p>简洁的结构和清晰的文档，让您能够快速构建Web应用程序。</p>
    </div>
    <div class="feature">
        <h2>AI辅助</h2>
        <p>集成AI辅助功能，提升开发效率和代码质量。</p>
    </div>
    <div class="feature">
        <h2>易于部署</h2>
        <p>简单的部署流程，让您的应用快速上线。</p>
    </div>
</div>

<div class="call-to-action">
    <h2>准备开始您的项目了吗？</h2>
    <button class="cta-button">立即开始</button>
</div>
<?php
$content = ob_get_clean();

// 引入布局模板
require_once ROOT_PATH . '/core/layouts/main.php';