<?php
/**
 * 联系我们页面
 * 展示各种联系方式，包括百度地图
 */

// 设置页面特定变量
$pageTitle = '联系我们 - AiPHP应用';
$pageDescription = '获取AiPHP应用的联系方式，包括地址、电话、邮箱等信息';
$pageKeywords = '联系我们,联系方式,地址,电话,邮箱,百度地图';

// 引入对应的CSS和JS文件
$additionalCSS = ['/static/own/css/contact/index.css'];
$additionalJS = ['/static/own/js/contact/index.js'];

// 页面内容
ob_start();
?>
<div class="contact-container">
    <h1 class="contact-title">联系我们</h1>
    
    <section class="contact-info">
        <div class="contact-item">
            <h2>公司地址</h2>
            <p>北京市朝阳区科技园区创新大厦1234室</p>
        </div>
        
        <div class="contact-item">
            <h2>联系电话</h2>
            <p>电话: 010-12345678</p>
            <p>手机: 138-0013-8000</p>
        </div>
        
        <div class="contact-item">
            <h2>电子邮箱</h2>
            <p><EMAIL></p>
            <p><EMAIL></p>
        </div>
        
        <div class="contact-item">
            <h2>工作时间</h2>
            <p>周一至周五: 9:00 - 18:00</p>
            <p>周六: 10:00 - 16:00</p>
            <p>周日: 休息</p>
        </div>
    </section>
    
    <section class="map-section">
        <h2>公司位置</h2>
        <div class="map-container">
            <!-- 百度地图 -->
            <iframe 
                src="https://map.baidu.com/" 
                width="100%" 
                height="400" 
                frameborder="0" 
                style="border:0;" 
                allowfullscreen="" 
                aria-hidden="false" 
                tabindex="0">
            </iframe>
        </div>
    </section>
    
    <section class="contact-methods">
        <h2>其他联系方式</h2>
        <div class="methods-grid">
            <div class="method-item">
                <h3>微信</h3>
                <p>扫描二维码关注我们</p>
                <div class="qr-code-placeholder">
                    <p>微信二维码</p>
                </div>
            </div>
            
            <div class="method-item">
                <h3>QQ</h3>
                <p>QQ群: 123456789</p>
                <p>在线客服: 987654321</p>
            </div>
            
            <div class="method-item">
                <h3>微博</h3>
                <p>@AiPHP官方</p>
                <p>欢迎关注我们的官方微博</p>
            </div>
        </div>
    </section>
</div>
<?php
$content = ob_get_clean();

// 载入绿色布局文件
require LAYOUTS_PATH . '/green_layout.php';
?>