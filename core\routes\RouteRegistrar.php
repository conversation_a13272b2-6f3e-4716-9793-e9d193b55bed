<?php

namespace Core\Routes;

/**
 * 路由注册类
 * 负责注册和管理路由规则
 */
class RouteRegistrar
{
    /**
     * 存储所有路由规则
     * @var array
     */
    private static array $routes = [
        'GET' => [],
        'POST' => [],
        'PUT' => [],
        'DELETE' => [],
        'PATCH' => []
    ];

    /**
     * 注册GET路由
     * @param string $pattern 路由模式
     * @param string $handler 处理器
     * @return void
     */
    public static function get(string $pattern, string $handler): void
    {
        self::addRoute('GET', $pattern, $handler);
    }

    /**
     * 注册POST路由
     * @param string $pattern 路由模式
     * @param string $handler 处理器
     * @return void
     */
    public static function post(string $pattern, string $handler): void
    {
        self::addRoute('POST', $pattern, $handler);
    }

    /**
     * 注册PUT路由
     * @param string $pattern 路由模式
     * @param string $handler 处理器
     * @return void
     */
    public static function put(string $pattern, string $handler): void
    {
        self::addRoute('PUT', $pattern, $handler);
    }

    /**
     * 注册DELETE路由
     * @param string $pattern 路由模式
     * @param string $handler 处理器
     * @return void
     */
    public static function delete(string $pattern, string $handler): void
    {
        self::addRoute('DELETE', $pattern, $handler);
    }

    /**
     * 注册PATCH路由
     * @param string $pattern 路由模式
     * @param string $handler 处理器
     * @return void
     */
    public static function patch(string $pattern, string $handler): void
    {
        self::addRoute('PATCH', $pattern, $handler);
    }

    /**
     * 添加路由到注册表
     * @param string $method HTTP方法
     * @param string $pattern 路由模式
     * @param string $handler 处理器
     * @return void
     */
    private static function addRoute(string $method, string $pattern, string $handler): void
    {
        self::$routes[$method][$pattern] = $handler;
    }

    /**
     * 获取所有路由
     * @return array
     */
    public static function getRoutes(): array
    {
        return self::$routes;
    }
}