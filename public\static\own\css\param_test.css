/* 参数处理测试页面样式 */

.param-test-content {
    padding: 20px 0;
    max-width: 1200px;
    margin: 0 auto;
}

.param-test-content h1 {
    text-align: center;
    margin-bottom: 2rem;
    color: #2c3e50;
    font-size: 2.5rem;
    font-weight: 600;
    position: relative;
    padding-bottom: 15px;
}

.param-test-content h1:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, #3498db, #2ecc71);
    border-radius: 2px;
}

.param-test-content h2 {
    color: #34495e;
    margin: 2rem 0 1rem;
    font-size: 1.8rem;
    font-weight: 500;
    position: relative;
    padding-left: 15px;
}

.param-test-content h2:before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 24px;
    background: #3498db;
    border-radius: 2px;
}

.param-test-content h3 {
    color: #34495e;
    margin: 1.5rem 0 1rem;
    font-size: 1.4rem;
    font-weight: 500;
}

.param-test-content p {
    color: #555;
    line-height: 1.7;
    font-size: 1.1rem;
    margin-bottom: 1rem;
}

/* 参数示例区域 */
.param-test-content ul {
    list-style-type: none;
    padding: 0;
    margin: 0 0 2rem 0;
}

.param-test-content li {
    padding: 12px 15px;
    margin-bottom: 8px;
    background: #f8f9fa;
    border-radius: 8px;
    transition: all 0.3s ease;
    border-left: 4px solid #3498db;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.param-test-content li:hover {
    background: #edf7ff;
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0,0,0,0.1);
    border-left-color: #2ecc71;
}

.param-test-content a {
    color: #3498db;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.param-test-content a:hover {
    color: #2980b9;
    text-decoration: underline;
}

.param-test-content a:visited {
    color: #9b59b6;
}

/* 参数显示区域 */
.params-display {
    background: #ffffff;
    padding: 25px;
    border-radius: 12px;
    margin-bottom: 2rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    border: 1px solid #eee;
}

.params-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.params-table thead {
    background: linear-gradient(135deg, #3498db, #2c3e50);
    color: white;
}

.params-table th {
    padding: 15px 20px;
    text-align: left;
    font-weight: 500;
}

.params-table tbody tr {
    border-bottom: 1px solid #eee;
    transition: background 0.3s ease;
}

.params-table tbody tr:nth-child(even) {
    background-color: #f9f9f9;
}

.params-table tbody tr:hover {
    background-color: #edf7ff;
}

.params-table td {
    padding: 12px 20px;
    color: #555;
}

.params-table td:first-child {
    font-weight: 600;
    color: #2c3e50;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .param-test-content {
        padding: 15px;
    }
    
    .param-test-content h1 {
        font-size: 2rem;
    }
    
    .param-test-content h2 {
        font-size: 1.5rem;
    }
    
    .params-display {
        padding: 15px;
    }
    
    .params-table {
        font-size: 0.9rem;
    }
    
    .params-table th, .params-table td {
        padding: 10px 12px;
    }
    
    .param-test-content li {
        padding: 10px 12px;
    }
}

@media (max-width: 480px) {
    .param-test-content h1 {
        font-size: 1.7rem;
    }
    
    .param-test-content h2 {
        font-size: 1.3rem;
    }
    
    .params-table {
        display: block;
        overflow-x: auto;
    }
}