/* 关于页面样式 */

/* 页面头部区域 */
.about-hero {
    text-align: center;
    padding: 3rem 0;
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    margin: -2rem -2rem 3rem -2rem;
    border-radius: 0 0 20px 20px;
}

.about-hero h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    font-weight: 700;
}

.hero-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

/* 主要内容区域 */
.about-content {
    max-width: 1000px;
    margin: 0 auto;
}

.about-content section {
    margin-bottom: 4rem;
}

.about-content h2 {
    font-size: 2rem;
    color: #333;
    margin-bottom: 1.5rem;
    text-align: center;
    position: relative;
}

.about-content h2::after {
    content: '';
    display: block;
    width: 60px;
    height: 3px;
    background: #007bff;
    margin: 0.5rem auto;
    border-radius: 2px;
}

/* 框架介绍网格 */
.intro-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.intro-item {
    background: #fff;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border-left: 4px solid #007bff;
}

.intro-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.intro-item h3 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.intro-item p {
    color: #666;
    line-height: 1.6;
}

/* 对比表格样式 */
.comparison-intro {
    text-align: center;
    color: #666;
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

.comparison-table-container {
    margin: 2rem auto;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    overflow: visible;
    max-width: 1000px;
}

.comparison-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    table-layout: fixed;
}

.comparison-table th,
.comparison-table td {
    padding: 0.8rem;
    text-align: center;
    border-bottom: 1px solid #e9ecef;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 0.9rem;
}

.comparison-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
    position: sticky;
    top: 0;
    z-index: 10;
    font-size: 0.85rem;
}

.comparison-table th.highlight {
    background: #007bff;
    color: white;
}

.comparison-table td.highlight {
    background: #e3f2fd;
    font-weight: 600;
    color: #0056b3;
}

.comparison-table tr:hover {
    background: #f8f9fa;
}

.comparison-table th:first-child,
.comparison-table td:first-child {
    text-align: left;
    font-weight: 500;
    background: #f8f9fa;
    white-space: nowrap;
    width: 120px;
    font-size: 0.85rem;
}

.comparison-table th:not(:first-child),
.comparison-table td:not(:first-child) {
    width: calc((100% - 120px) / 5);
    min-width: 140px;
}

/* 选择理由网格 */
.reasons-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.reason-card {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.reason-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.reason-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.reason-card h3 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.reason-card p {
    color: #666;
    line-height: 1.6;
}

/* 开始使用区域 */
.get-started {
    text-align: center;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 3rem 2rem;
    border-radius: 12px;
    margin-top: 3rem;
}

.get-started h2 {
    margin-bottom: 1rem;
}

.get-started p {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 2rem;
}

/* 按钮样式 */
.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.btn {
    display: inline-block;
    padding: 0.75rem 2rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.btn-primary {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.btn-primary:hover {
    background: #0056b3;
    border-color: #0056b3;
    transform: translateY(-2px);
}

.btn-secondary {
    background: #6c757d;
    color: white;
    border-color: #6c757d;
}

.btn-secondary:hover {
    background: #545b62;
    border-color: #545b62;
    transform: translateY(-2px);
}

.btn-outline {
    background: transparent;
    color: #007bff;
    border-color: #007bff;
}

.btn-outline:hover {
    background: #007bff;
    color: white;
    transform: translateY(-2px);
}

/* 动画和交互效果 */
.column-highlight {
    background-color: #e3f2fd !important;
    transition: background-color 0.2s ease;
}

.animate-in {
    animation: fadeInUp 0.6s ease forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 波纹效果 */
.btn {
    position: relative;
    overflow: hidden;
}

.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* 表格滚动指示器 */
.comparison-table-container.scrollable {
    position: relative;
}

.scroll-indicator {
    position: absolute;
    bottom: -30px;
    left: 50%;
    transform: translateX(-50%);
    background: #007bff;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 0.7;
    }
    50% {
        opacity: 1;
    }
}

/* 动画暂停状态 */
.animations-paused * {
    animation-play-state: paused !important;
    transition: none !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .about-hero {
        margin: -1rem -1rem 2rem -1rem;
        padding: 2rem 1rem;
    }
    
    .about-hero h1 {
        font-size: 2rem;
    }
    
    .hero-subtitle {
        font-size: 1rem;
    }
    
    .intro-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .intro-item {
        padding: 1.5rem;
    }
    
    .comparison-table-container {
        margin: 1rem -1rem;
        overflow-x: auto;
    }

    .comparison-table {
        min-width: 600px;
    }

    .comparison-table th:not(:first-child),
    .comparison-table td:not(:first-child) {
        min-width: 100px;
        font-size: 0.9rem;
    }
    
    .reasons-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .reason-card {
        padding: 1.5rem;
    }
    
    .get-started {
        padding: 2rem 1rem;
        margin: 2rem -1rem 0 -1rem;
    }
    
    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .btn {
        width: 200px;
    }
}

@media (max-width: 480px) {
    .about-hero h1 {
        font-size: 1.8rem;
    }
    
    .about-content h2 {
        font-size: 1.5rem;
    }
    
    .comparison-table th,
    .comparison-table td {
        padding: 0.5rem;
        font-size: 0.9rem;
    }
}
