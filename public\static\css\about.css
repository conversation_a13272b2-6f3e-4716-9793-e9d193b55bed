.about-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

.hero-section {
    text-align: center;
    padding: 4rem 0;
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    color: white;
    border-radius: 12px;
    margin-bottom: 3rem;
}

.hero-section h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.subtitle {
    font-size: 1.5rem;
    opacity: 0.9;
}

.features-section,
.comparison-section,
.why-choose-section {
    margin-bottom: 4rem;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.feature-item {
    padding: 2rem;
    background: #f8fafc;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.feature-item h3 {
    color: #4f46e5;
    margin-bottom: 1rem;
}

.comparison-table {
    overflow-x: auto;
    margin-top: 2rem;
}

table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

th, td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #e2e8f0;
}

th {
    background: #f8fafc;
    font-weight: 600;
}

td:first-child {
    font-weight: 500;
}

.advantages-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.advantage-item {
    padding: 2rem;
    background: #f8fafc;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    text-align: center;
}

.advantage-item h3 {
    color: #4f46e5;
    margin-bottom: 1rem;
}

@media (max-width: 768px) {
    .about-container {
        padding: 1rem;
    }
    
    .hero-section {
        padding: 2rem 1rem;
    }
    
    .hero-section h1 {
        font-size: 2rem;
    }
    
    .subtitle {
        font-size: 1.2rem;
    }
    
    table {
        font-size: 0.9rem;
    }
}
