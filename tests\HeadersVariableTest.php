<?php

declare(strict_types=1);

/**
 * Headers变量测试脚本
 * 
 * 测试从headers中获取mm变量的功能
 */

// 模拟getallheaders函数（在CLI环境下可能不存在）
if (!function_exists('getallheaders')) {
    function getallheaders() {
        $headers = [];
        foreach ($_SERVER as $key => $value) {
            if (substr($key, 0, 5) == 'HTTP_') {
                $header = str_replace(' ', '-', ucwords(str_replace('_', ' ', strtolower(substr($key, 5)))));
                $headers[$header] = $value;
            }
        }
        return $headers;
    }
}

// 模拟设置headers
$_SERVER['HTTP_MM'] = 'test-value-from-header';

// 测试获取headers中的mm变量
$headers = getallheaders();
$mmValue = isset($headers['mm']) ? $headers['mm'] : (isset($headers['Mm']) ? $headers['Mm'] : '');

echo "Headers中的mm变量值: " . ($mmValue ?: '未找到') . "\n";

// 验证是否正确获取
if ($mmValue === 'test-value-from-header') {
    echo "✓ 测试通过：成功获取headers中的mm变量\n";
} else {
    echo "✗ 测试失败：未能正确获取headers中的mm变量\n";
}

// 测试空值情况
unset($_SERVER['HTTP_MM']);
$headers = getallheaders();
$mmValue = isset($headers['mm']) ? $headers['mm'] : (isset($headers['Mm']) ? $headers['Mm'] : '');

if (empty($mmValue)) {
    echo "✓ 测试通过：正确处理空值情况\n";
} else {
    echo "✗ 测试失败：未能正确处理空值情况\n";
}

echo "\n所有测试完成!\n";