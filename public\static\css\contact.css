/* 联系页面专用样式 */

.contact-header {
    text-align: center;
    margin-bottom: 3rem;
    padding: 2rem 0;
}

.contact-header h1 {
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 1rem;
}

.contact-header p {
    font-size: 1.2rem;
    color: #666;
}

.contact-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.contact-info {
    padding: 1rem;
}

.contact-item {
    margin-bottom: 2rem;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.contact-item:hover {
    transform: translateY(-5px);
}

.contact-item h3 {
    color: #007bff;
    margin-bottom: 0.5rem;
    font-size: 1.3rem;
}

.contact-item p {
    color: #555;
    font-size: 1.1rem;
    line-height: 1.6;
}

.map-container {
    padding: 1rem;
}

.map-container h3 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.map-placeholder {
    background-color: #f8f9fa;
    border-radius: 8px;
    overflow: hidden;
}

.map-placeholder p {
    text-align: center;
    padding: 1rem;
    color: #666;
}

.map-placeholder iframe {
    width: 100%;
    height: 300px;
    border: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .contact-header h1 {
        font-size: 2rem;
    }
    
    .contact-header p {
        font-size: 1rem;
    }
    
    .contact-content {
        grid-template-columns: 1fr;
    }
    
    .contact-item {
        padding: 1rem;
    }
}