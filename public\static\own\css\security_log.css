/* 安全日志页面样式 */

.security-log-container {
    padding: 20px 0;
    max-width: 1200px;
    margin: 0 auto;
}

.log-title {
    text-align: center;
    margin-bottom: 2rem;
    color: #2d6a4f;
    font-size: 2rem;
}

.log-intro {
    background: #d8f3dc;
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 2rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.log-intro h2 {
    color: #2d6a4f;
    margin-bottom: 1rem;
}

.log-content {
    background: #fff;
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 2rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-left: 4px solid #40916c;
}

.log-content h2 {
    color: #2d6a4f;
    margin-bottom: 1rem;
}

.log-data {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 4px;
    overflow-x: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
    max-height: 500px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.4;
}

.no-logs {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 2rem;
}

.log-actions {
    background: #fffae6;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.log-actions h2 {
    color: #2d6a4f;
    margin-bottom: 1rem;
}

.action-buttons {
    display: flex;
    gap: 1rem;
}

.action-buttons button {
    padding: 0.5rem 1rem;
    background: #40916c;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s;
}

.action-buttons button:hover {
    background: #2d6a4f;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .security-log-container {
        padding: 10px;
    }
    
    .log-title {
        font-size: 1.5rem;
    }
    
    .log-intro,
    .log-content,
    .log-actions {
        padding: 1rem;
    }
    
    .action-buttons {
        flex-direction: column;
    }
}