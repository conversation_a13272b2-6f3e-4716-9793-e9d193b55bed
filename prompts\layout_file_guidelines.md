# AiPHP 布局文件提示词指南

## 概述
本指南详细说明了在AiPHP框架中创建和使用布局文件的规范和最佳实践，确保布局文件的一致性、可维护性和可用性。请严格按照本指南创建布局文件，创建后无需进行测试。

## 布局文件命名规范
- 布局文件应放置在`handler/layouts/`目录下
- 文件名使用小写字母，单词间用下划线分隔
- 主布局文件通常命名为`main.php`
- 特殊用途布局文件应清晰反映其用途，并统一添加`_layout`后缀，如`admin_layout.php`、`login_layout.php`等
- 颜色主题布局文件命名为`颜色名称_layout.php`，例如：`green_layout.php`、`red_layout.php`

## 静态资源文件规范
- CSS文件应放置在`public/static/own/css/`目录下
- JavaScript文件应放置在`public/static/own/js/`目录下
- 图片和其他静态资源应放置在`public/static/own/images/`目录下
- 文件名使用小写字母，单词间用连字符分隔
- **每个布局文件必须有对应的CSS和JS文件**，文件名应与布局文件保持一致，例如：
  - 主布局文件`main.php`对应`main.css`和`main.js`
  - 绿色主题布局`green_layout.php`对应`green_layout.css`和`green_layout.js`
  - 红色主题布局`red_layout.php`对应`red_layout.css`和`red_layout.js`
  - 管理员布局`admin_layout.php`对应`admin_layout.css`和`admin_layout.js`

## 布局文件结构规范
布局文件应分为两个主要部分：
1. **PHP逻辑部分**：处理页面标题、元数据、CSS/JS资源等
2. **HTML模板部分**：完整的HTML结构，包含头部、主要内容区域和底部

## PHP逻辑部分规范
- 使用PHP变量来接收页面传递的数据
- 设置默认值以确保布局文件的独立性
- 变量命名应清晰明确，如`$pageTitle`、`$content`等
- 不应在布局文件中处理复杂的业务逻辑

### 核心变量
```php
<?php
// 默认页面标题
$pageTitle = $pageTitle ?? '默认标题';

// 默认页面描述
$pageDescription = $pageDescription ?? '默认描述';

// 默认关键字
$pageKeywords = $pageKeywords ?? '默认关键字';

// 页面内容
$content = $content ?? '';

// 额外CSS文件数组
$additionalCSS = $additionalCSS ?? [];

// 额外JS文件数组
$additionalJS = $additionalJS ?? [];
?>
```

## HTML模板部分规范
- 使用HTML5文档类型
- 包含完整的HTML结构：`<!DOCTYPE html>`、`<html>`、`<head>`、`<body>`
- 在`<head>`部分包含必要的meta标签和资源链接
- 在页面内容区域使用`<?php echo $content; ?>`输出页面内容
- 确保布局响应式设计

### 基本HTML结构
```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($pageTitle); ?></title>
    <meta name="description" content="<?php echo htmlspecialchars($pageDescription); ?>">
    <meta name="keywords" content="<?php echo htmlspecialchars($pageKeywords); ?>">
    
    <!-- 布局文件对应的CSS -->
    <!-- 注意：这里应该引用与布局文件同名的CSS文件 -->
    <!-- 例如：main.php布局应引用main.css，green_layout.php布局应引用green_layout.css，red_layout.php布局应引用red_layout.css -->
    <link rel="stylesheet" href="/static/own/css/main.css">
    
    <!-- 额外的CSS文件 -->
    <?php foreach ($additionalCSS as $css): ?>
        <link rel="stylesheet" href="<?php echo htmlspecialchars($css); ?>">
    <?php endforeach; ?>
</head>
<body>
    <!-- 页面头部 -->
    <!-- 注意：创建新布局文件时，必须保留main.php布局文件中的页面头部结构，包括导航菜单 -->
    <header class="site-header">
        <div class="container">
            <h1 class="site-title"><a href="/"><?php echo htmlspecialchars($siteName); ?></a></h1>
            <nav class="site-nav">
                <ul>
                    <li><a href="/">首页</a></li>
                    <li><a href="/about">关于</a></li>
                    <li><a href="/contact">联系</a></li>
                </ul>
            </nav>
        </div>
    </header>
    
    <!-- 主要内容区域 -->
    <main>
        <?php echo $content; ?>
    </main>
    
    <!-- 页面底部 -->
    <footer>
        <!-- 底部内容 -->
    </footer>
    
    <!-- 布局文件对应的JS -->
    <!-- 注意：这里应该引用与布局文件同名的JS文件 -->
    <!-- 例如：main.php布局应引用main.js，green_layout.php布局应引用green_layout.js，red_layout.php布局应引用red_layout.js -->
    <script src="/static/own/js/main.js"></script>
    
    <!-- 额外的JS文件 -->
    <?php foreach ($additionalJS as $js): ?>
        <script src="<?php echo htmlspecialchars($js); ?>"></script>
    <?php endforeach; ?>
</body>
</html>
```

## 页面文件使用布局规范
页面文件应使用以下方式来使用布局文件：

### 变量设置
```php
<?php
// 设置页面特定变量
$pageTitle = '页面标题';
$pageDescription = '页面描述';
$pageKeywords = '关键字1, 关键字2';
$additionalCSS = ['/static/own/css/page.css'];
$additionalJS = ['/static/own/js/page.js'];

// 页面内容 
ob_start();
?>
<div class="page-content">
    <h1>页面标题</h1>
    <p>页面内容</p>
</div>
<?php
$content = ob_get_clean();
?>
```

### 布局加载
```php
<?php
// 直接载入布局文件
require LAYOUTS_PATH . '/main.php';
?>
```

### 替换布局文件
要为页面更换使用的布局文件，只需：

1. **找到要应用布局的页面**：定位到需要更改布局的PHP页面文件（通常位于`handler/pages/`目录下）

2. **替换布局引用**：将页面中的布局引用代码替换为目标布局文件名即可

例如，将页面从使用主布局更换为红色主题布局：
```php
<?php
// 从使用主布局
// require LAYOUTS_PATH . '/main.php';

// 更换为使用红色主题布局
require LAYOUTS_PATH . '/red_layout.php';
?>
```

或者更换为管理员布局：
```php
<?php
// 更换为使用管理员布局
require LAYOUTS_PATH . '/admin_layout.php';
?>
```

## 禁用布局规范
在某些特殊情况下，可能需要为特定页面禁用布局，直接输出完整的HTML页面。要禁用布局，请遵循以下规范：

### 1. 移除布局引用
- 从页面文件中移除`require LAYOUTS_PATH . '/layout_file.php';`语句
- 移除$content变量的使用

### 2. 直接输出完整HTML
- 页面文件应直接输出完整的HTML结构，包括`<!DOCTYPE html>`声明
- 在PHP代码块之外直接编写HTML代码
- 保留必要的页面元信息（标题、描述、关键词等）

### 3. 内联样式和脚本
- 对于禁用布局的页面，可以使用内联CSS和JavaScript
- 确保页面在没有外部资源的情况下仍能正常显示和运行

### 4. 示例
禁用布局的页面文件示例：
```php
<?php
// 页面变量设置
$pageTitle = '独立页面 - AiPHP应用';
$pageDescription = '这是一个禁用布局的独立页面';
$pageKeywords = '独立页面, 无布局';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($pageTitle); ?></title>
    <meta name="description" content="<?php echo htmlspecialchars($pageDescription); ?>">
    <meta name="keywords" content="<?php echo htmlspecialchars($pageKeywords); ?>">
    <style>
        /* 内联CSS样式 */
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>独立页面</h1>
        <p>这是一个禁用布局的页面，直接输出完整的HTML。</p>
    </div>
    
    <script>
        // 内联JavaScript代码
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成');
        });
    </script>
</body>
</html>
```

## 静态资源文件创建规范
**在创建布局文件时，必须同时创建对应的CSS和JS文件**，文件名应与布局文件保持一致。

### CSS文件创建规范
每个布局文件都必须有对应的CSS文件，路径和命名规则如下：
- 主布局文件`main.php`对应的CSS文件路径：`public/static/own/css/main.css`
- 绿色主题布局`green_layout.php`对应的CSS文件路径：`public/static/own/css/green_layout.css`
- 红色主题布局`red_layout.php`对应的CSS文件路径：`public/static/own/css/red_layout.css`
- 管理员布局`admin_layout.php`对应的CSS文件路径：`public/static/own/css/admin_layout.css`

CSS文件示例（以main.css为例）：
```css
/* 主样式表 */
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    /* 移动端样式 */
}
```

### JS文件创建规范
每个布局文件都必须有对应的JS文件，路径和命名规则如下：
- 主布局文件`main.php`对应的JS文件路径：`public/static/own/js/main.js`
- 绿色主题布局`green_layout.php`对应的JS文件路径：`public/static/own/js/green_layout.js`
- 红色主题布局`red_layout.php`对应的JS文件路径：`public/static/own/js/red_layout.js`
- 管理员布局`admin_layout.php`对应的JS文件路径：`public/static/own/js/admin_layout.js`

JS文件示例（以main.js为例）：
```javascript
// 主JavaScript文件
document.addEventListener('DOMContentLoaded', function() {
    // 页面加载完成后执行的代码
});
```

## 布局文件删除规范
当需要删除一个布局文件时，应遵循以下步骤以确保系统的完整性和一致性：

### 1. 删除指定的布局文件
- 删除位于`handler/layouts/`目录下的目标布局文件
- 例如：删除`handler/layouts/custom_layout.php`或`handler/layouts/red_layout.php`

### 2. 删除布局文件对应的静态文件
- **必须删除与布局文件同名的CSS和JS文件**
- CSS文件位于`public/static/own/css/`目录
- JS文件位于`public/static/own/js/`目录
- 例如：删除`custom_layout.php`时，应同时删除`public/static/own/css/custom_layout.css`和`public/static/own/js/custom_layout.js`
- 例如：删除`red_layout.php`时，应同时删除`public/static/own/css/red_layout.css`和`public/static/own/js/red_layout.js`

### 3. 查找并更新应用该布局的页面
- 使用搜索工具查找所有引用该布局文件的页面文件
- 将所有引用从目标布局文件更改为`main.php`（或其他适当的布局文件）
- 例如：将`require LAYOUTS_PATH . '/custom_layout.php';`更改为`require LAYOUTS_PATH . '/main.php';`
- 例如：将`require LAYOUTS_PATH . '/red_layout.php';`更改为`require LAYOUTS_PATH . '/main.php';`
- 确保更新后页面显示正常，没有样式或功能问题

## 最佳实践建议
1. **安全性**：在输出变量时使用`htmlspecialchars()`防止XSS攻击
2. **默认值**：为所有布局变量设置合理的默认值
3. **资源管理**：合理管理CSS和JS资源，避免重复加载
4. **响应式设计**：确保布局在各种设备上都能正常显示
5. **语义化标签**：使用HTML5语义化标签提高可访问性
6. **性能优化**：压缩CSS和JS文件，使用CDN加速静态资源
7. **可维护性**：保持布局文件简洁，避免过于复杂的嵌套
8. **目录结构**：严格按照AiPHP框架目录结构规范组织文件

## 示例
完整的布局使用示例：

页面文件 (`handler/pages/example/index.php`):
```php
<?php
// 设置页面变量
$pageTitle = '示例页面 - AiPHP应用';
$pageDescription = '这是一个示例页面';
$pageKeywords = '示例, AiPHP';

// 页面内容
ob_start();
?>
<div class="example-page">
    <h1>示例页面</h1>
    <p>这是页面内容</p>
</div>
<?php
$content = ob_get_clean();

// 载入布局
require LAYOUTS_PATH . '/main.php';
?>
```

布局文件 (`handler/layouts/main.php`):
```php
<?php
$pageTitle = $pageTitle ?? '默认标题';
$pageDescription = $pageDescription ?? '默认描述';
$content = $content ?? '';
$additionalCSS = $additionalCSS ?? [];
$additionalJS = $additionalJS ?? [];
?>
<!DOCTYPE html>
<html>
<head>
    <title><?php echo htmlspecialchars($pageTitle); ?></title>
    <meta name="description" content="<?php echo htmlspecialchars($pageDescription); ?>">
    <!-- 主要:应引用与布局文件同名的CSS文件 -->
    <!-- 例如：main.php布局应引用main.css，green_layout.php布局应引用green_layout.css，red_layout.php布局应引用red_layout.css -->
    <?php foreach ($additionalCSS as $css): ?>
        <link rel="stylesheet" href="<?php echo htmlspecialchars($css); ?>">
    <?php endforeach; ?>
</head>
<body>
    <main>
        <?php echo $content; ?>
    </main>
    <!-- 注意，应引用与布局文件同名的JS文件 -->
    <!-- 例如：main.php布局应引用main.js，green_layout.php布局应引用green_layout.js，red_layout.php布局应引用red_layout.js -->
    <?php foreach ($additionalJS as $js): ?>
        <script src="<?php echo htmlspecialchars($js); ?>"></script>
    <?php endforeach; ?>
</body>
</html>
```