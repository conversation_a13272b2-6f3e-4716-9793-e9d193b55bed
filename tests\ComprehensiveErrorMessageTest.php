<?php

declare(strict_types=1);

/**
 * 全面的错误信息测试
 */

// 包含必要的文件
require_once __DIR__ . '/../core/own-library/autoloader/autoloader.php';

// 注册自动加载器
use Core\OwnLibrary\Autoloader\Autoloader;

$autoloader = new Autoloader();
$autoloader->addNamespace('Core\OwnLibrary', __DIR__ . '/../core/own-library');
$autoloader->register();

// 使用命名空间
use Core\OwnLibrary\Validation\ValidationHelper;

// 测试各种验证规则的错误信息
$tests = [
    // 类型验证
    ['params' => ['id' => 'abc'], 'field' => 'id', 'rules' => ['数字'], 'expected' => '必须是数字'],
    ['params' => ['name' => 123], 'field' => 'name', 'rules' => ['字符串'], 'expected' => '必须是字符串'],
    
    // 格式验证
    ['params' => ['email' => 'invalid'], 'field' => 'email', 'rules' => ['邮箱'], 'expected' => '必须是邮箱格式'],
    ['params' => ['url' => 'invalid'], 'field' => 'url', 'rules' => ['网址'], 'expected' => '必须是网址格式'],
    ['params' => ['phone' => 'invalid'], 'field' => 'phone', 'rules' => ['手机'], 'expected' => '必须是手机号格式'],
    
    // 范围验证
    ['params' => ['age' => 'abc'], 'field' => 'age', 'rules' => ['范围18-65'], 'expected' => '必须是数值'],
    ['params' => ['score' => '150'], 'field' => 'score', 'rules' => ['范围0-100'], 'expected' => '不能大于100'],
    
    // 长度验证
    ['params' => ['code' => '123'], 'field' => 'code', 'rules' => ['长度6'], 'expected' => '长度不能少于6个字符'],
];

echo "全面错误信息测试:\n\n";

$passed = 0;
$total = count($tests);

foreach ($tests as $index => $test) {
    $result = ValidationHelper::validate($test['params'], $test['field'], $test['rules']);
    $actualError = $result[$test['field']] ?? '';
    
    if ($actualError === $test['expected']) {
        echo ($index + 1) . ". ✓ 通过\n";
        echo "   规则: " . json_encode($test['rules']) . "\n";
        echo "   期望: " . $test['expected'] . "\n";
        echo "   实际: " . $actualError . "\n\n";
        $passed++;
    } else {
        echo ($index + 1) . ". ✗ 失败\n";
        echo "   规则: " . json_encode($test['rules']) . "\n";
        echo "   期望: " . $test['expected'] . "\n";
        echo "   实际: " . $actualError . "\n\n";
    }
}

echo "测试结果: $passed/$total 通过\n";
echo "通过率: " . round(($passed / $total) * 100, 2) . "%\n";