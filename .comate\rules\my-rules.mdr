---
description: AiPHP项目规则
globs: 
alwaysApply: true
---

当用户说"根据提示词"或"根据XX提示词"时，请按照以下步骤操作：

1. 首先识别用户想要使用的提示词文件，提示词文件位于项目的`prompts/`目录下
2. **必须首先读取`prompts/master_guidelines.md`作为项目的总纲**，理解其中的框架规范、目录结构和核心原则
3. 如果用户明确指定了提示词类型（如"根据布局提示词"），则：
   - 在`prompts/`目录下搜索包含相关关键词的提示词文件
   - 例如："根据布局提示词"应匹配包含"layout"关键词的提示词文件
   - 例如："根据核心类提示词"应匹配包含"class"或"core"关键词的提示词文件
   - 例如："根据测试提示词"应匹配包含"test"关键词的提示词文件
4. 如果找到多个匹配文件或没有明确指定，则自动搜索并列出`prompts/`目录下的所有提示词文件，供用户选择
5. 读取选定的提示词文件内容，深入理解其中的规范和要求
6. 根据用户的具体需求、总纲规范和具体提示词中的指导原则，生成相应的代码
7. 确保生成的代码符合提示词中规定的命名规范、结构规范和最佳实践

在生成代码时，请特别注意以下要点：
- 严格遵循提示词中规定的文件命名规范和目录结构
- 确保代码注释完整且符合要求
- 遵循指定的编码规范（如PSR-12等）
- 保持与项目现有代码风格的一致性
- 根据需要创建相应的配套文件（如CSS、JS等）
- 始终遵循`master_guidelines.md`中定义的核心原则和框架约束

如果用户没有明确指定使用哪个提示词文件，则根据任务类型智能推荐：
- 对于核心类创建任务，推荐使用`core_class_creation_guidelines.md`
- 对于布局文件创建任务，推荐使用`layout_file_guidelines.md`
- 对于测试相关任务，推荐使用`test_guidelines.md`
- 对于其他任务，推荐参考`master_guidelines.md`

系统会随着项目发展不断添加新的提示词文件，因此需要动态搜索和识别`prompts/`目录下的所有文件。