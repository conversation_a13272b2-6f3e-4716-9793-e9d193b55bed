/* 联系我们页面样式 */

.contact-container {
    padding: 20px 0;
}

.contact-title {
    text-align: center;
    margin-bottom: 2rem;
    color: #2d6a4f;
    font-size: 2rem;
}

.contact-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.contact-item {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.contact-item:hover {
    transform: translateY(-5px);
}

.contact-item h2 {
    color: #40916c;
    margin-bottom: 1rem;
    font-size: 1.4rem;
}

.contact-item p {
    margin-bottom: 0.5rem;
    color: #555;
}

.map-section {
    margin-bottom: 3rem;
}

.map-section h2 {
    color: #2d6a4f;
    margin-bottom: 1rem;
    font-size: 1.6rem;
}

.map-container {
    height: 400px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.contact-methods {
    margin-bottom: 2rem;
}

.contact-methods h2 {
    color: #2d6a4f;
    margin-bottom: 1.5rem;
    font-size: 1.6rem;
}

.methods-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.method-item {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.method-item h3 {
    color: #40916c;
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.method-item p {
    margin-bottom: 0.5rem;
    color: #555;
}

.qr-code-placeholder {
    background: #f0f0f0;
    height: 150px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    margin-top: 1rem;
}

.qr-code-placeholder p {
    color: #888;
    font-style: italic;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .contact-container {
        padding: 10px;
    }
    
    .contact-title {
        font-size: 1.8rem;
    }
    
    .contact-info {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .methods-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .map-container {
        height: 300px;
    }
}