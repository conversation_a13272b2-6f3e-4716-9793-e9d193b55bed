<?php
// Web路由配置文件

use Core\Routes\RouteRegistrar;

// 静态路由
RouteRegistrar::get('/', 'home.php');
RouteRegistrar::get('/home', 'home.php');
RouteRegistrar::get('/users', 'users.php');
RouteRegistrar::get('/404', '404.php');

// 参数化路由
RouteRegistrar::get('/users/{id:number}', 'users.php');
RouteRegistrar::get('/users/page/{page:number}', 'users.php');

// POST路由示例（用于表单提交）
RouteRegistrar::post('/users/create', 'users.php');
RouteRegistrar::post('/users/{id:number}/update', 'users.php');

// DELETE路由示例（用于删除操作）
RouteRegistrar::delete('/users/{id:number}', 'users.php');

// PUT路由示例（用于更新操作）
RouteRegistrar::put('/users/{id:number}', 'users.php');