<?php

namespace Core\Security;

/**
 * CSRF防护类
 * 
 * 提供CSRF令牌生成和验证功能，防止跨站请求伪造攻击
 * 
 * @category    Core
 * @package     Security
 * <AUTHOR> Assistant
 * @version     1.0.0
 * 
 * @example     生成CSRF令牌: $token = CSRF::generateToken();
 * @example     验证CSRF令牌: $isValid = CSRF::verifyToken($token);
 * @example     验证请求: $isValid = CSRF::verifyRequest($_POST);
 * @example     渲染隐藏字段: echo CSRF::renderHiddenField();
 * 
 * Class CSRF
 */
class CSRF
{
    /**
     * 会话中存储令牌的键名
     */
    private const SESSION_TOKEN_KEY = 'csrf_token';
    
    /**
     * 表单中令牌字段的名称
     */
    private const FORM_TOKEN_FIELD = 'csrf_token';
    
    /**
     * 生成CSRF令牌
     * 
     * @return string CSRF令牌
     * 
     * @example
     * $token = CSRF::generateToken();
     */
    public static function generateToken(): string
    {
        // 确保会话已启动
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        // 如果会话中还没有令牌，则生成一个
        if (!isset($_SESSION[self::SESSION_TOKEN_KEY])) {
            $_SESSION[self::SESSION_TOKEN_KEY] = bin2hex(random_bytes(32));
        }
        
        return $_SESSION[self::SESSION_TOKEN_KEY];
    }
    
    /**
     * 验证CSRF令牌
     * 
     * @param string $token 待验证的令牌
     * @return bool 验证结果
     * 
     * @example
     * $isValid = CSRF::verifyToken($token);
     */
    public static function verifyToken(string $token): bool
    {
        // 确保会话已启动
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        // 检查会话中是否存在令牌
        if (!isset($_SESSION[self::SESSION_TOKEN_KEY])) {
            return false;
        }
        
        // 使用hash_equals防止时序攻击
        return hash_equals($_SESSION[self::SESSION_TOKEN_KEY], $token);
    }
    
    /**
     * 验证请求中的CSRF令牌
     * 
     * @param array $requestData 请求数据（如$_POST）
     * @return bool 验证结果
     * 
     * @example
     * $isValid = CSRF::verifyRequest($_POST);
     */
    public static function verifyRequest(array $requestData): bool
    {
        $token = $requestData[self::FORM_TOKEN_FIELD] ?? '';
        return self::verifyToken($token);
    }
    
    /**
     * 生成包含CSRF令牌的隐藏表单字段
     * 
     * @return string HTML隐藏字段
     * 
     * @example
     * echo CSRF::renderHiddenField();
     */
    public static function renderHiddenField(): string
    {
        $token = self::generateToken();
        return '<input type="hidden" name="' . self::FORM_TOKEN_FIELD . '" value="' . htmlspecialchars($token, ENT_QUOTES, 'UTF-8') . '">';
    }
    
    /**
     * 获取CSRF令牌字段名称
     * 
     * @return string 令牌字段名称
     * 
     * @example
     * $fieldName = CSRF::getTokenFieldName();
     */
    public static function getTokenFieldName(): string
    {
        return self::FORM_TOKEN_FIELD;
    }
    
    /**
     * 获取CSRF令牌值
     * 
     * @return string CSRF令牌值
     * 
     * @example
     * $token = CSRF::getToken();
     */
    public static function getToken(): string
    {
        // 确保会话已启动
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        return $_SESSION[self::SESSION_TOKEN_KEY] ?? '';
    }
}