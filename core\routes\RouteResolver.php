<?php

namespace Core\Routes;

/**
 * 路由解析类
 * 负责解析URL并匹配对应的路由处理器
 */
class RouteResolver
{
    /**
     * 解析URL并返回匹配的路由信息
     * @param string $requestUri 请求URI
     * @param string $requestMethod HTTP请求方法
     * @return array|null 包含处理器和参数的数组，未匹配则返回null
     */
    public static function resolve(string $requestUri, string $requestMethod): ?array
    {
        // 移除查询字符串
        $requestUri = strtok($requestUri, '?') ?: $requestUri;
        
        // 获取所有注册的路由
        $routes = RouteRegistrar::getRoutes();
        
        // 检查是否存在该HTTP方法的路由
        if (!isset($routes[$requestMethod])) {
            return null;
        }
        
        // 遍历该HTTP方法的所有路由
        foreach ($routes[$requestMethod] as $pattern => $handler) {
            // 尝试匹配路由
            $result = self::matchRoute($pattern, $requestUri);
            if ($result !== false) {
                return [
                    'handler' => $handler,
                    'params' => $result
                ];
            }
        }
        
        return null;
    }
    
    /**
     * 匹配路由模式和请求URI
     * @param string $pattern 路由模式
     * @param string $uri 请求URI
     * @return array|false 匹配的参数数组，未匹配则返回false
     */
    private static function matchRoute(string $pattern, string $uri)
    {
        // 处理静态路由（无参数）
        if ($pattern === $uri) {
            return [];
        }
        
        // 处理参数化路由
        if (strpos($pattern, '{') !== false) {
            return self::matchParameterizedRoute($pattern, $uri);
        }
        
        return false;
    }
    
    /**
     * 匹配参数化路由
     * @param string $pattern 路由模式
     * @param string $uri 请求URI
     * @return array|false 匹配的参数数组，未匹配则返回false
     */
    private static function matchParameterizedRoute(string $pattern, string $uri)
    {
        $params = [];
        
        // 将路由模式转换为正则表达式
        $patternParts = explode('/', trim($pattern, '/'));
        $uriParts = explode('/', trim($uri, '/'));
        
        // 如果段落数不匹配，直接返回false
        if (count($patternParts) !== count($uriParts)) {
            return false;
        }
        
        // 逐段匹配
        for ($i = 0; $i < count($patternParts); $i++) {
            $patternPart = $patternParts[$i];
            $uriPart = $uriParts[$i];
            
            // 检查是否为参数段
            if (preg_match('/\{([^}:]+)(?::([^}]+))?\}/', $patternPart, $matches)) {
                $paramName = $matches[1];
                $paramType = $matches[2] ?? null;
                
                // 验证参数类型
                if ($paramType && !self::validateParamType($uriPart, $paramType)) {
                    return false;
                }
                
                // 添加参数到结果数组
                $params[$paramName] = $uriPart;
            } else {
                // 静态段必须完全匹配
                if ($patternPart !== $uriPart) {
                    return false;
                }
            }
        }
        
        return $params;
    }
    
    /**
     * 验证参数类型
     * @param string $value 参数值
     * @param string $type 参数类型
     * @return bool 是否匹配类型
     */
    private static function validateParamType(string $value, string $type): bool
    {
        switch ($type) {
            case 'number':
                return is_numeric($value) && ctype_digit($value);
            case 'string':
                return is_string($value);
            case 'email':
                return filter_var($value, FILTER_VALIDATE_EMAIL) !== false;
            default:
                return true; // 未知类型默认通过验证
        }
    }
}