// 关于页面交互功能

document.addEventListener('DOMContentLoaded', function() {
    // 页面加载动画
    initPageAnimations();
    
    // 表格交互功能
    initTableInteractions();
    
    // 按钮点击事件
    initButtonEvents();
    
    // 滚动动画
    initScrollAnimations();
});

// 初始化页面动画
function initPageAnimations() {
    // 为卡片添加渐入动画
    const cards = document.querySelectorAll('.intro-item, .reason-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        
        setTimeout(() => {
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
}

// 初始化表格交互
function initTableInteractions() {
    const table = document.querySelector('.comparison-table');
    if (!table) return;
    
    // 添加表格行悬停效果
    const rows = table.querySelectorAll('tbody tr');
    rows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.02)';
            this.style.transition = 'transform 0.2s ease';
        });
        
        row.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
        });
    });
    
    // 添加表格列高亮功能
    const cells = table.querySelectorAll('th, td');
    cells.forEach((cell, index) => {
        cell.addEventListener('mouseenter', function() {
            const columnIndex = this.cellIndex;
            highlightColumn(table, columnIndex, true);
        });
        
        cell.addEventListener('mouseleave', function() {
            const columnIndex = this.cellIndex;
            highlightColumn(table, columnIndex, false);
        });
    });
}

// 高亮表格列
function highlightColumn(table, columnIndex, highlight) {
    const rows = table.querySelectorAll('tr');
    rows.forEach(row => {
        const cell = row.cells[columnIndex];
        if (cell) {
            if (highlight) {
                cell.classList.add('column-highlight');
            } else {
                cell.classList.remove('column-highlight');
            }
        }
    });
}

// 初始化按钮事件
function initButtonEvents() {
    const buttons = document.querySelectorAll('.btn');
    
    buttons.forEach(button => {
        // 添加点击波纹效果
        button.addEventListener('click', function(e) {
            createRippleEffect(this, e);
        });
        
        // 添加悬停效果
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px) scale(1.05)';
        });
        
        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
}

// 创建波纹效果
function createRippleEffect(button, event) {
    const ripple = document.createElement('span');
    const rect = button.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = event.clientX - rect.left - size / 2;
    const y = event.clientY - rect.top - size / 2;
    
    ripple.style.width = ripple.style.height = size + 'px';
    ripple.style.left = x + 'px';
    ripple.style.top = y + 'px';
    ripple.classList.add('ripple');
    
    button.appendChild(ripple);
    
    setTimeout(() => {
        ripple.remove();
    }, 600);
}

// 初始化滚动动画
function initScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);
    
    // 观察需要动画的元素
    const animateElements = document.querySelectorAll('.framework-intro, .framework-comparison, .why-choose, .get-started');
    animateElements.forEach(el => {
        observer.observe(el);
    });
}

// 平滑滚动到锚点
function smoothScrollTo(targetId) {
    const target = document.getElementById(targetId);
    if (target) {
        target.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// 添加表格响应式处理
function handleTableResponsive() {
    const table = document.querySelector('.comparison-table');
    const container = document.querySelector('.comparison-table-container');
    
    if (!table || !container) return;
    
    // 检查表格是否溢出
    if (table.scrollWidth > container.clientWidth) {
        container.classList.add('scrollable');
        
        // 添加滚动指示器
        if (!container.querySelector('.scroll-indicator')) {
            const indicator = document.createElement('div');
            indicator.className = 'scroll-indicator';
            indicator.innerHTML = '← 左右滑动查看更多 →';
            container.appendChild(indicator);
        }
    }
}

// 窗口大小改变时重新检查表格
window.addEventListener('resize', handleTableResponsive);

// 添加键盘导航支持
document.addEventListener('keydown', function(e) {
    // 按ESC键回到顶部
    if (e.key === 'Escape') {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    }
    
    // 按空格键暂停/继续动画
    if (e.key === ' ' && e.target === document.body) {
        e.preventDefault();
        toggleAnimations();
    }
});

// 切换动画状态
function toggleAnimations() {
    const body = document.body;
    if (body.classList.contains('animations-paused')) {
        body.classList.remove('animations-paused');
    } else {
        body.classList.add('animations-paused');
    }
}

// 添加性能优化
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 优化滚动事件
const optimizedScrollHandler = debounce(() => {
    handleTableResponsive();
}, 100);

window.addEventListener('scroll', optimizedScrollHandler);
