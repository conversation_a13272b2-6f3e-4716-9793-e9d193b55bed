# AiPHP 框架

AiPHP 是一个现代化的PHP开发框架，结合了人工智能辅助开发的理念，旨在为开发者提供高效、简洁且功能强大的开发体验。

## 项目概述

- **项目名称**: AiPHP
- **项目类型**: PHP Web开发框架
- **核心理念**: 结合人工智能辅助开发
- **目标用户**: PHP开发者

## 功能特性

- 简洁的架构设计，让您专注于业务逻辑开发
- 结合人工智能技术，提供智能代码生成和优化建议
- 轻量级设计，便于部署和维护
- 响应式布局支持
- 模块化架构设计
- 内置核心功能类支持常见开发任务

## 目录结构

```
AiPHP/
├── app/                 # 应用目录
│   ├── layouts/         # 布局模板目录
│   └── pages/           # 页面文件目录
├── core/                # 核心目录
│   ├── database/        # 数据库相关类
│   ├── routes/          # 路由相关类和配置
│   └── bootstrap.php    # 核心类加载引导文件
├── public/              # 公共目录
│   ├── static/          # 静态资源目录
│   └── index.php        # 入口文件
├── database/            # 数据库文件目录
└── documents/           # 文档文件目录（可选）
```

## 系统要求

- PHP 8.0 或更高版本
- 支持URL重写的Web服务器（Apache/Nginx/IIS）
- MySQL 8.0+ 或 SQLite 3+（可选，用于数据库功能）

## 安装部署

1. 将项目文件上传到Web服务器目录
2. 确保 `public` 目录为Web访问根目录
3. 配置Web服务器，将所有请求指向 `public/index.php`
4. 确保 `storage` 目录具有写入权限（如果使用文件存储功能）

## 服务器配置说明

> **重要提示**：本项目默认配置为在IIS服务器上运行。当AI需要测试功能时，应直接通过网址访问测试，无需启动PHP内置服务器。
> 
> 如果配置中指明需要使用PHP内置服务器，AI可以自行启动。启动命令如下：
> ```
> php -S localhost:8000 -t public
> ```
> 然后通过 `http://localhost:8000` 访问应用。

## AI开发说明

> **重要提示**：当AI需要执行开发任务时，应首先阅读项目提示词文件 `prompt_templates.md`,`prompt_templates_layout.md`，该文件包含了各种开发任务的标准提示词模板。按照提示词模板的要求进行开发工作，可以确保代码质量和项目规范的一致性。

### Apache 配置示例

```apache
<VirtualHost *:80>
    DocumentRoot "/path/to/AiPHP/public"
    ServerName your-domain.com
    
    <Directory "/path/to/AiPHP/public">
        AllowOverride All
        Require all granted
    </Directory>
</VirtualHost>
```

### Nginx 配置示例

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/AiPHP/public;
    index index.php;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }
}
```

## 使用说明

### 创建新页面

1. 在 `app/pages/` 目录下创建新的PHP文件
2. 在 `public/static/css/` 目录下创建对应的CSS文件
3. 在 `public/static/js/` 目录下创建对应的JS文件
4. 在 `core/routes/web.php` 中添加路由配置
5. 在布局文件的导航菜单中添加链接

### 创建新布局

1. 在 `app/layouts/` 目录下创建新的布局文件
2. 在 `public/static/css/` 目录下创建对应的CSS文件
3. 更新页面文件以使用新布局

### 使用文件阅读器

项目包含一个内置的文件阅读器功能，可以查看 `documents/` 目录下的文本文件。

1. 将需要阅读的文件放入 `documents/` 目录
2. 通过 `/reader?file=文件名` 访问文件
3. 支持的文件类型：txt, md, html, css, js, php

例如：`/reader?file=example.txt`

### 数据库配置

1. 在 `core/database/` 目录下配置数据库连接信息
2. 使用内置的数据库适配器（MySQL/SQLite）进行数据库操作

## 核心组件

### 路由系统
- 配置文件：`core/routes/web.php`
- 支持参数化路由和静态路由

### 模板系统
- 布局模板：`app/layouts/`
- 页面文件：`app/pages/`

### 数据库系统
- 连接管理：`core/database/Connection.php`
- 查询构建器：`core/database/QueryBuilder.php`
- 数据库适配器：MySQL和SQLite支持

### 核心功能类
- 过滤验证：`core/filters/Filter.php`
- 其他辅助功能类

## 开发规范

### 命名规范
- 类名使用帕斯卡命名法（PascalCase）
- 方法名使用驼峰命名法（camelCase）
- 变量名使用驼峰命名法（camelCase）
- 常量名使用大写字母和下划线（UPPER_CASE）

### 代码规范
- 使用PHP原生语法，不依赖外部框架
- 遵循PSR-4自动加载规范
- 保持代码简洁和可读性

## 常见问题

### 1. 页面无法访问
- 检查路由配置是否正确
- 确认Web服务器配置是否正确
- 检查文件权限设置

### 2. 数据库连接失败
- 检查数据库配置信息
- 确认数据库服务是否正常运行
- 检查网络连接和防火墙设置

### 3. 静态资源加载失败
- 检查文件路径是否正确
- 确认Web服务器配置是否正确
- 检查文件权限设置

## 贡献指南

欢迎提交Issue和Pull Request来改进AiPHP框架。

1. Fork项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启Pull Request

## 许可证

本项目采用MIT许可证，详情请见 [LICENSE](LICENSE) 文件。

## 联系方式

- 项目地址: [https://github.com/your-username/AiPHP](https://github.com/your-username/AiPHP)
- 邮箱: <EMAIL>

---

© 2025 AI PHP. 保留所有权利。