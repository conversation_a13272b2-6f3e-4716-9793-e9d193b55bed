# AI PHP框架

AI PHP是一个现代化的PHP开发框架，结合了人工智能辅助开发的理念，旨在为开发者提供高效、简洁且功能强大的开发体验。

## 目录结构

```
.
├── public                  # Web服务器公共目录
│   ├── index.php           # 入口文件
│   └── static              # 静态资源目录
│       ├── css             # CSS样式文件
│       └── js              # JavaScript文件
├── core                    # 核心代码目录
│   ├── layouts             # 布局模板目录
│   ├── routes              # 路由配置目录
│   ├── pages               # 页面文件目录
│   └── ...
└── README.md               # 项目说明文件
```

## 标准操作提示词模板

### 1. 生成布局模板文件

```
请帮我生成一个布局模板文件：
- 布局名称：[在这里填写布局名称，如"主布局"、"管理后台布局"等]
- 布局要求：[在这里详细描述布局的设计需求，包括结构、样式、功能等]
- 输出文件：core/layouts/[布局文件名.php]

要求：
1. 根据布局要求生成完整的HTML布局模板
2. 使用语义化的HTML结构
3. 为该布局创建专用的CSS文件，保存到public/static/css/目录下，文件名为[布局文件名.css]
4. 为该布局创建专用的JS文件，保存到public/static/js/目录下，文件名为[布局文件名.js]
5. 在布局模板中通过<link>和<script>标签引入对应的CSS和JS文件（不要将CSS和JS代码写在布局文件里）
6. 使用PHP变量接收页面标题和内容（标题变量为$title，内容变量为$content）
7. 添加一个PHP变量$pageName用于接收页面文件名（不含.php扩展名），用于引入页面专用CSS和JS
8. 生成完整的PHP布局模板文件
```

**示例：**
```
请帮我生成一个布局模板文件：
- 布局名称：主布局
- 布局要求：创建一个现代化的网站布局，包含顶部导航栏（首页、关于、联系链接）、主要内容区域和页脚。导航栏要有响应式设计，在小屏幕上变为汉堡菜单。页脚包含版权信息。
- 输出文件：core/layouts/main.php
```

### 2. 生成页面文件

```
请帮我生成一个页面文件：
- 页面名称：[在这里填写页面名称，如"首页"、"关于我们"等]
- 页面要求：[在这里详细描述页面的内容需求，包括功能、样式、交互等]
- 路由路径：[页面对应的路由路径，如"/"、"/about"等]
- 输出文件：core/pages/[页面文件名.php]

要求：
1. 在core/routes/web.php文件中添加对应的路由规则
2. 页面文件应调用core/layouts/main.php布局文件
3. 根据页面要求生成具体的页面内容
4. 将生成的页面文件保存到core/pages文件夹中
5. 为该页面创建专用的CSS文件，保存到public/static/css/目录下，文件名为[页面文件名.css]
6. 为该页面创建专用的JS文件，保存到public/static/js/目录下，文件名为[页面文件名.js]
7. 页面标题通过PHP变量$title传递给布局模板
8. 页面内容通过PHP变量$content传递给布局模板
9. 页面文件名（不含.php扩展名）通过PHP变量$pageName传递给布局模板，用于引入对应的CSS和JS文件
10. 如果布局文件中没有对应的菜单项，需要在导航菜单中添加该页面的链接
11. 生成完整的PHP页面文件
```

**示例：**
```
请帮我生成一个页面文件：
- 页面名称：网站首页
- 页面要求：创建一个吸引人的网站首页，包含欢迎横幅、主要特性介绍和行动号召按钮。特性包括"快速开发"、"AI辅助"、"易于部署"等。
- 路由路径：/
- 输出文件：core/pages/home.php
```

### 3. 删除页面文件

```
请帮我删除一个页面文件：
- 页面名称：[在这里填写要删除的页面名称，如"首页"、"关于我们"等]
- 页面文件：core/pages/[页面文件名.php]

要求：
1. 从core/routes/web.php文件中删除对应的路由规则
2. 从布局文件（如core/layouts/main.php）中删除对应的导航菜单项
3. 删除core/pages/目录下的页面文件
4. 删除public/static/css/目录下对应的CSS文件（[页面文件名.css]）
5. 删除public/static/js/目录下对应的JS文件（[页面文件名.js]）
6. 确保所有相关引用都被正确移除
```

**示例：**
```
请帮我删除一个页面文件：
- 页面名称：关于我们
- 页面文件：core/pages/about.php