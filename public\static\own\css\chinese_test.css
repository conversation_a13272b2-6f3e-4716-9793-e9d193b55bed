/* 中文测试页面样式 */
.chinese-test-content {
    padding: 20px;
    font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
}

.chinese-test-content h1 {
    color: #2c3e50;
    border-bottom: 2px solid #3498db;
    padding-bottom: 10px;
}

.chinese-test-content h2 {
    color: #34495e;
    margin-top: 30px;
}

.params-display {
    background-color: #f8f9fa;
    border-radius: 5px;
    padding: 15px;
    margin: 15px 0;
}

.params-table {
    width: 100%;
    border-collapse: collapse;
    margin: 10px 0;
}

.params-table th,
.params-table td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}

.params-table th {
    background-color: #e9ecef;
    font-weight: bold;
}

.greeting-message,
.product-message {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    border-radius: 5px;
    padding: 15px;
    margin: 15px 0;
}

.greeting-message p,
.product-message p {
    margin: 5px 0;
}