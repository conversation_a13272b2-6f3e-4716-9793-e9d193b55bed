// 联系我们页面的JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // 联系方式卡片悬停效果增强
    const contactItems = document.querySelectorAll('.contact-item');
    contactItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.15)';
        });
        
        item.addEventListener('mouseleave', function() {
            this.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.1)';
        });
    });
    
    // 联系方式方法卡片悬停效果
    const methodItems = document.querySelectorAll('.method-item');
    methodItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.boxShadow = '0 6px 15px rgba(0, 0, 0, 0.15)';
        });
        
        item.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.1)';
        });
    });
    
    // 地图加载完成后的处理
    const mapContainer = document.querySelector('.map-container');
    if (mapContainer) {
        // 添加加载动画
        mapContainer.style.opacity = '0';
        mapContainer.style.transition = 'opacity 0.5s ease';
        
        // 模拟地图加载完成
        setTimeout(() => {
            mapContainer.style.opacity = '1';
        }, 500);
    }
    
    // 平滑滚动到联系信息部分
    const contactLinks = document.querySelectorAll('a[href="#contact-info"]');
    contactLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector('#contact-info');
            if (target) {
                window.scrollTo({
                    top: target.offsetTop - 100,
                    behavior: 'smooth'
                });
            }
        });
    });
    
    // 表单提交处理（如果将来添加表单）
    const contactForm = document.querySelector('.contact-form');
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            // 这里可以添加表单验证和提交逻辑
            alert('感谢您的联系！我们会尽快回复您。');
        });
    }
});