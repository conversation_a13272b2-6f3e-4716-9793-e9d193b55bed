/* 安全测试页面样式 */

.security-test-container {
    padding: 20px 0;
    max-width: 1200px;
    margin: 0 auto;
}

.test-title {
    text-align: center;
    margin-bottom: 2rem;
    color: #2d6a4f;
    font-size: 2rem;
}

.test-intro {
    background: #d8f3dc;
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 2rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.test-intro h2 {
    color: #2d6a4f;
    margin-bottom: 1rem;
}

.test-category {
    background: #fff;
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 2rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-left: 4px solid #40916c;
}

.test-category h2 {
    color: #2d6a4f;
    margin-bottom: 1rem;
}

.test-list {
    list-style-type: none;
    padding: 0;
}

.test-list li {
    padding: 0.75rem 0;
    border-bottom: 1px solid #e9ecef;
}

.test-list li:last-child {
    border-bottom: none;
}

.test-list a {
    color: #40916c;
    text-decoration: none;
    font-weight: 500;
}

.test-list a:hover {
    text-decoration: underline;
}

.test-list a.danger {
    color: #d90429;
}

.test-list a.danger:hover {
    color: #ff0000;
}

.test-info {
    background: #fffae6;
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 2rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.test-info h2 {
    color: #2d6a4f;
    margin-bottom: 1rem;
}

.test-info ul {
    list-style-type: none;
    padding: 0;
}

.test-info li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #fff9c4;
}

.test-info li:last-child {
    border-bottom: none;
}

.test-info strong {
    color: #2d6a4f;
}

.test-explanation {
    background: #e8f4fc;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.test-explanation h2 {
    color: #2d6a4f;
    margin-bottom: 1rem;
}

.test-explanation ul {
    list-style-type: disc;
    padding-left: 1.5rem;
}

.test-explanation li {
    padding: 0.25rem 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .security-test-container {
        padding: 10px;
    }
    
    .test-title {
        font-size: 1.5rem;
    }
    
    .test-category,
    .test-info,
    .test-explanation {
        padding: 1rem;
    }
}