<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <system.webServer>
        <defaultDocument>
            <files>
                <clear />
                <add value="index.php" />
            </files>
        </defaultDocument>
        <directoryBrowse enabled="false" />
        <httpProtocol>
            <customHeaders>
            </customHeaders>
        </httpProtocol>
        <staticContent>
            <mimeMap fileExtension=".md" mimeType="text/markdown" />
        </staticContent>
        <rewrite>
            <rules>
                <!-- 静态资源直接访问 -->
                <rule name="Static Assets" stopProcessing="true">
                    <match url="^static/(.*)" />
                    <action type="Rewrite" url="static/{R:1}" />
                </rule>
                
                <!-- 允许直接访问docs.html -->
                <rule name="Docs Page" stopProcessing="true">
                    <match url="^docs\.html$" />
                    <action type="None" />
                </rule>
                
                <!-- 允许直接访问.md文件 -->
                <rule name="Markdown Files" stopProcessing="true">
                    <match url="^.*\.md$" />
                    <action type="None" />
                </rule>
                
                <!-- 将所有其他请求都重写到index.php，实现单入口模式 -->
                <rule name="Single Entry Point" stopProcessing="true">
                    <match url=".*" />
                    <action type="Rewrite" url="index.php" />
                </rule>
            </rules>
        </rewrite>
    </system.webServer>
</configuration>