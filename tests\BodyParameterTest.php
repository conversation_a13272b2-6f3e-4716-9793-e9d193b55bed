<?php

declare(strict_types=1);

/**
 * Body参数测试脚本
 * 
 * 测试从body中获取cc参数的功能
 */

// 模拟php://input数据
$testInput = 'cc=test-value-from-body&other_param=other_value';

// 模拟file_get_contents('php://input')
function mock_file_get_contents($stream) {
    global $testInput;
    if ($stream === 'php://input') {
        return $testInput;
    }
    // 实际实现会调用原始函数
    return file_get_contents($stream);
}

// 模拟parse_str函数来解析输入数据
function parse_input_data($input) {
    $data = [];
    parse_str($input, $data);
    return $data;
}

// 测试获取body中的cc参数
$input = $testInput;
$postData = parse_input_data($input);
$ccValue = isset($postData['cc']) ? $postData['cc'] : '';

echo "Body中的cc参数值: " . ($ccValue ?: '未找到') . "\n";

// 验证是否正确获取
if ($ccValue === 'test-value-from-body') {
    echo "✓ 测试通过：成功获取body中的cc参数\n";
} else {
    echo "✗ 测试失败：未能正确获取body中的cc参数\n";
}

// 测试空值情况
$emptyInput = '';
$postData = parse_input_data($emptyInput);
$ccValue = isset($postData['cc']) ? $postData['cc'] : '';

if (empty($ccValue)) {
    echo "✓ 测试通过：正确处理空值情况\n";
} else {
    echo "✗ 测试失败：未能正确处理空值情况\n";
}

// 测试没有cc参数的情况
$noCcInput = 'other_param=other_value';
$postData = parse_input_data($noCcInput);
$ccValue = isset($postData['cc']) ? $postData['cc'] : '';

if (empty($ccValue)) {
    echo "✓ 测试通过：正确处理没有cc参数的情况\n";
} else {
    echo "✗ 测试失败：未能正确处理没有cc参数的情况\n";
}

echo "\n所有测试完成!\n";