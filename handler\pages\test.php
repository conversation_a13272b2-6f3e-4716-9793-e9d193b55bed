<?php
/**
 * 测试页面 - 可以接受参数的页面
 * 遵循AiPHP框架页面管理规范
 * 使用绿色布局
 */

// 设置页面特定变量
$pageTitle = '测试页面 - 带参数 - AiPHP应用';
$pageDescription = '这是一个可以接受URL参数和路径参数的测试页面，用于演示AiPHP框架的参数处理功能';
$pageKeywords = '测试,参数,URL参数,路径参数,AiPHP,框架';

// 如果使用布局，需要引入对应的CSS和JS文件
$additionalCSS = ['/static/own/css/test.css'];
$additionalJS = ['/static/own/js/test.js'];

// 直接访问从PageLoader传递过来的参数
// 直接使用$params变量,这是所有路由参数的数组.也可以直接循环出这里的参数
// 直接使用路由参数  比如:路由配置里是 '/test/{id}' => 'test',   这里就可以直接调用 $id??'';

// 引入ValidationHelper类
use Core\OwnLibrary\Validation\ValidationHelper;

// 验证路由参数中的ID字段
// 将$params数组传递给验证器，验证器根据字段名和规则进行验证
$idValidationResult = ValidationHelper::validate($params, 'id', [
    '必填'=>"ID必须填写",  // 可以只写规则名，使用默认错误信息,如果填写了错误信息，则使用填写的错误信息
    '数字' =>"必须是数字", // 可以只写规则名，使用默认错误信息
    '长度5'=>"长度必须为5",  // 修正：正确的长度验证写法
    '最小值20000'=>"最小值必须为20000",  // 修正：正确的最小值验证写法
]);

// 获取headers中的mm变量
$headers = getallheaders();
$mmValue = isset($headers['mm']) ? $headers['mm'] : (isset($headers['Mm']) ? $headers['Mm'] : '');

// 获取cc参数
$ccValue = '';

// 首先检查URL查询参数
if (isset($_GET['cc'])) {
    $ccValue = $_GET['cc'];
}
// 然后检查POST参数
elseif (isset($_POST['cc'])) {
    $ccValue = $_POST['cc'];
}
// 最后检查请求体参数（包括GET请求的Body数据）
else {
    // 获取原始的输入数据
    $input = file_get_contents('php://input');
    if (!empty($input)) {
        // 判断内容类型
        $contentType = $_SERVER['CONTENT_TYPE'] ?? '';
        
        // 处理form-data格式
        if (strpos($contentType, 'multipart/form-data') !== false) {
            // 注意：php://input无法直接读取multipart/form-data
            // 因为GET请求中的form-data很特殊，我们需要尝试多种方式
            // 1. 尝试使用$_FILES
            if (isset($_FILES['cc']) && $_FILES['cc']['error'] === UPLOAD_ERR_OK) {
                $ccValue = file_get_contents($_FILES['cc']['tmp_name']);
            }
            // 2. 尝试使用$_REQUEST
            elseif (isset($_REQUEST['cc'])) {
                $ccValue = $_REQUEST['cc'];
            }
        }
        // 处理x-www-form-urlencoded格式
        elseif (strpos($contentType, 'application/x-www-form-urlencoded') !== false) {
            parse_str($input, $postData);
            if (isset($postData['cc'])) {
                $ccValue = $postData['cc'];
            }
        }
        // 处理JSON格式
        elseif (strpos($contentType, 'application/json') !== false) {
            $jsonData = json_decode($input, true);
            if (json_last_error() === JSON_ERROR_NONE && isset($jsonData['cc'])) {
                $ccValue = $jsonData['cc'];
            }
        }
        // 其他格式，尝试直接解析
        else {
            parse_str($input, $data);
            if (isset($data['cc'])) {
                $ccValue = $data['cc'];
            }
        }
    }
}

// 调试信息
$debugInfo = [
    'GET' => $_GET,
    'POST' => $_POST,
    'FILES' => $_FILES,
    'REQUEST' => $_REQUEST,
    'RAW_INPUT' => file_get_contents('php://input'),
    'CONTENT_TYPE' => $_SERVER['CONTENT_TYPE'] ?? '未设置',
    'REQUEST_METHOD' => $_SERVER['REQUEST_METHOD'] ?? '未设置',
    'CC_VALUE' => $ccValue
];

// 页面内容
ob_start();
?>
<?php echo $id??'';?>
<div class="test-page-content">
    <h1>测试页面</h1>
    <p>这是一个可以接受URL参数的测试页面。您可以通过在URL中添加参数来查看不同的效果。</p>
    
    <?php 
    // 显示ID验证结果
    echo $idValidationResult['id'] ?? "";
    ?>
    
    <!-- 显示headers中的mm变量 -->
    <?php if (!empty($mmValue)): ?>
    <div class="mm-display">
        <h2>Headers中的mm变量</h2>
        <p><strong>mm:</strong> <?php echo htmlspecialchars($mmValue); ?></p>
    </div>
    <?php endif; ?>
    
    <!-- 显示cc参数 -->
    <?php if (!empty($ccValue)): ?>
    <div class="cc-display">
        <h2>cc参数值</h2>
        <p><strong>cc:</strong> <?php echo htmlspecialchars($ccValue); ?></p>
    </div>
    <?php endif; ?>
    
    <!-- 显示调试信息 -->
    <div class="debug-info" style="margin-top: 20px; padding: 15px; background-color: #f8f9fa; border: 1px solid #ddd; border-radius: 4px;">
        <h3>调试信息</h3>
        <pre style="background-color: #eee; padding: 10px; overflow: auto;"><?php print_r($debugInfo); ?></pre>
    </div>
    <h2>参数示例</h2>
    <p>尝试访问以下链接：</p>
    <ul>
        <li><a href="/test">基本测试页面</a></li>
        <li><a href="/test?name=张三">带查询参数（?name=张三）</a></li>
        <li><a href="/test?id=123">带查询参数（?id=123）</a></li>
        <li><a href="/test?name=李四&id=456&type=advanced">带多个查询参数</a></li>
        <li><a href="/test/123">带路径参数（/test/123，其中123是id参数）</a></li>
        <li><a href="/test/123/product-name">带多个路径参数（/test/123/product-name，其中123是id，product-name是slug）</a></li>
        <li><a href="/test/123?name=王五">混合参数（路径参数+查询参数）</a></li>
        <li><a href="/test/param1/param2/param3/param4/param5">5个路径参数测试（/test/param1/param2/param3/param4/param5）</a></li>
        <li><a href="/test/p1/p2/p3/p4/p5/p6/p7/p8/p9" style="color:red;">⚠️ 超过路由参数限制测试（9个参数，超过了系统限制的8个）</a></li>
    </ul>
    
    <h2>从PageLoader直接传递的参数</h2>
    <div class="params-display">
        <?php if (!empty($params)): ?>
            <p>这些参数是通过PageLoader::loadPage()方法直接传递到页面的：</p>
            <table class="params-table">
                <thead>
                    <tr>
                        <th>参数名</th>
                        <th>参数值</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($params as $key => $value): ?>
                        <tr>
                            <td><?php echo $key; ?></td>
                            <td><?php 
                                if (is_array($value)) {
                                    echo print_r($value, true);
                                } else {
                                    echo $value;
                                }
                            ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php else: ?>
            <p>当前没有通过PageLoader直接传递的参数。</p>
        <?php endif; ?>
    </div>

    <h2>所有接收到的参数（包括查询参数）</h2>
    <div class="params-display">
        <?php 
        // 合并路由参数和查询参数
        $allParams = array_merge($_GET, $params);
        ?>
        <?php if (!empty($allParams)): ?>
            <p>页面接收到以下参数：</p>
            <table class="params-table">
                <thead>
                    <tr>
                        <th>参数名</th>
                        <th>参数值</th>
                        <th>参数类型</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($allParams as $key => $value): ?>
                        <tr>
                            <td><?php echo $key; ?></td>
                            <td><?php 
                                if (is_array($value)) {
                                    echo print_r($value, true);
                                } else {
                                    echo $value;
                                }
                            ?></td>
                            <td><?php echo isset($params[$key]) ? '路径参数' : '查询参数'; ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php else: ?>
            <p>没有接收到任何参数。请尝试在URL中添加参数，例如：<code>/test?name=张三</code> 或使用路径参数 <code>/test/123</code></p>
        <?php endif; ?>
    </div>

    <h2>直接变量访问示例</h2>
    <div class="params-display">
        <?php if (isset($id)): ?>
            <p><strong>ID参数直接访问：</strong> ID = <?php 
                // 直接输出，ParameterProcessor中已经处理过了
                echo $id;
            ?></p>
        <?php else: ?>
            <p>没有接收到ID参数</p>
        <?php endif; ?>
        
        <?php if (isset($slug)): ?>
            <p><strong>Slug参数直接访问：</strong> Slug = <?php 
                // 直接输出，ParameterProcessor中已经处理过了
                echo $slug;
            ?></p>
        <?php else: ?>
            <p>没有接收到Slug参数</p>
        <?php endif; ?>
        
        <?php if (isset($param1, $param2, $param3, $param4, $param5)): ?>
            <h3>5个路由参数测试结果：</h3>
            <ul>
                <li><strong>参数1 (param1):</strong> <?php echo $param1; ?></li>
                <li><strong>参数2 (param2):</strong> <?php echo $param2; ?></li>
                <li><strong>参数3 (param3):</strong> <?php echo $param3; ?></li>
                <li><strong>参数4 (param4):</strong> <?php echo $param4; ?></li>
                <li><strong>参数5 (param5):</strong> <?php echo $param5; ?></li>
            </ul>
        <?php endif; ?>
    </div>
    
    <h2>参数处理演示</h2>
    <div class="params-demo">
        <?php 
        // 演示参数处理
        $message = '';
        if (isset($allParams['name'])) {
            $message = "您好，" . $allParams['name'] . "！";
        }
        if (isset($allParams['id'])) {
            $message .= " 您的ID是：" . $allParams['id'] . "。";
        }
        if (isset($allParams['slug'])) {
            $message .= " 您的slug是：" . $allParams['slug'] . "。";
        }
        if (!empty($message)): ?>
            <div class="greeting-message">
                <?php echo $message; ?>
            </div>
        <?php else: ?>
            <p>没有可用的参数来生成个性化消息。请尝试使用带参数的URL访问此页面。</p>
        <?php endif; ?>
    </div>
    
    <h2>参数验证演示</h2>
    <div class="params-validation">
        <p>尝试访问以下链接进行验证测试：</p>
        <ul>
            <li><a href="/test?phone=13812345678">验证有效手机号（13812345678）</a></li>
            <li><a href="/test?phone=12345">验证无效手机号（长度不足）</a></li>
            <li><a href="/test?phone=abc12345678">验证无效手机号（包含非数字字符）</a></li>
            <li><a href="/test?id=123">验证有效ID（123）</a></li>
            <li><a href="/test?id=abc">验证无效ID（非数字）</a></li>
            <li><a href="/test?id=">验证空ID（空值）</a></li>
            <li><a href="/test?email=<EMAIL>">验证有效邮箱（<EMAIL>）</a></li>
            <li><a href="/test?email=invalid-email">验证无效邮箱（invalid-email）</a></li>
            <li><a href="/test?username=john">验证有效用户名（john）</a></li>
            <li><a href="/test?username=a">验证无效用户名（a，长度不足）</a></li>
        </ul>
    </div>
</div>
<?php
$content = ob_get_clean();

// 载入布局文件
require LAYOUTS_PATH . '/green_layout.php';
?>