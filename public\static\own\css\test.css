/* ===========================================
   测试页面样式 - 现代化设计
   =========================================== */

/* 基础样式重置 */
.test-page-content {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: #333;
  padding: 20px 0;
  max-width: 1200px;
  margin: 0 auto;
}

/* 页面标题 */
.test-page-content h1 {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  color: #2c3e50;
  position: relative;
  padding-bottom: 15px;
}

.test-page-content h1:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, #3498db, #2ecc71);
  border-radius: 2px;
}

/* 章节标题 */
.test-page-content h2 {
  font-size: 1.8rem;
  font-weight: 600;
  margin: 2.5rem 0 1.5rem;
  color: #34495e;
  position: relative;
  padding-left: 20px;
}

.test-page-content h2:before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 5px;
  height: 28px;
  background: linear-gradient(to bottom, #3498db, #9b59b6);
  border-radius: 2px;
}

.test-page-content h3 {
  font-size: 1.4rem;
  font-weight: 600;
  margin: 1.5rem 0 1rem;
  color: #34495e;
}

/* 段落文本 */
.test-page-content p {
  font-size: 1.1rem;
  color: #555;
  margin-bottom: 1.2rem;
  line-height: 1.7;
}

/* 参数示例列表 */
.test-page-content ul {
  list-style: none;
  padding: 0;
  margin: 0 0 2rem 0;
}

.test-page-content li {
  padding: 15px 20px;
  margin-bottom: 12px;
  background: #ffffff;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  border-left: 4px solid #3498db;
  position: relative;
  overflow: hidden;
}

.test-page-content li:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgba(52, 152, 219, 0.03), transparent);
  z-index: 0;
}

.test-page-content li:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
  border-left-color: #2ecc71;
}

.test-page-content li a {
  position: relative;
  z-index: 1;
  color: #3498db;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s ease;
}

.test-page-content li a:hover {
  color: #2980b9;
  text-decoration: underline;
}

/* 警告链接特殊样式 */
.test-page-content li a[style*="color:red"] {
  color: #e74c3c !important;
  font-weight: 600;
}

.test-page-content li a[style*="color:red"]:hover {
  color: #c0392b !important;
  text-decoration: none;
}

.test-page-content li a[style*="color:red"]:before {
  content: "⚠️ ";
  margin-right: 8px;
}

/* 参数显示区域 */
.params-display {
  background: #ffffff;
  padding: 25px;
  border-radius: 12px;
  margin-bottom: 2.5rem;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #eee;
  transition: all 0.3s ease;
}

.params-display:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.params-display h3 {
  margin-top: 0;
  color: #2c3e50;
}

.params-display p:first-child {
  margin-top: 0;
}

/* 表格样式 */
.params-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.params-table thead {
  background: linear-gradient(135deg, #3498db, #2c3e50);
  color: white;
}

.params-table th {
  padding: 16px 20px;
  text-align: left;
  font-weight: 500;
  font-size: 1.05rem;
}

.params-table tbody tr {
  border-bottom: 1px solid #eee;
  transition: background 0.3s ease;
}

.params-table tbody tr:nth-child(even) {
  background-color: #f9f9f9;
}

.params-table tbody tr:hover {
  background-color: #edf7ff;
}

.params-table td {
  padding: 14px 20px;
  color: #555;
  font-size: 0.95rem;
}

.params-table td:first-child {
  font-weight: 600;
  color: #2c3e50;
}

/* 参数演示区域 */
.params-demo {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 30px;
  border-radius: 12px;
  margin-bottom: 2.5rem;
  color: white;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.params-demo .greeting-message {
  font-size: 1.4rem;
  font-weight: 500;
  text-align: center;
  padding: 25px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 10px;
  backdrop-filter: blur(5px);
  margin: 0;
}

.params-demo p {
  color: rgba(255, 255, 255, 0.9);
  text-align: center;
  font-size: 1.1rem;
  margin: 0;
}

/* 代码显示样式 */
code {
  background: #2c3e50;
  color: #ecf0f1;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: 'Consolas', 'Courier New', monospace;
  font-size: 0.95rem;
}

/* 响应式设计 */
@media (max-width: 992px) {
  .test-page-content {
    padding: 15px;
  }
  
  .test-page-content h1 {
    font-size: 2.2rem;
  }
  
  .test-page-content h2 {
    font-size: 1.6rem;
  }
  
  .params-display {
    padding: 20px;
  }
  
  .params-table th, .params-table td {
    padding: 12px 15px;
  }
}

@media (max-width: 768px) {
  .test-page-content h1 {
    font-size: 2rem;
  }
  
  .test-page-content h2 {
    font-size: 1.5rem;
    padding-left: 15px;
  }
  
  .test-page-content h2:before {
    height: 24px;
  }
  
  .test-page-content p {
    font-size: 1rem;
  }
  
  .test-page-content li {
    padding: 12px 15px;
  }
  
  .params-display, .params-demo {
    padding: 20px 15px;
  }
  
  .params-table {
    display: block;
    overflow-x: auto;
  }
  
  .params-table th, .params-table td {
    padding: 10px 12px;
    font-size: 0.9rem;
  }
  
  .params-demo .greeting-message {
    font-size: 1.2rem;
    padding: 20px 15px;
  }
}

@media (max-width: 480px) {
  .test-page-content h1 {
    font-size: 1.8rem;
  }
  
  .test-page-content h2 {
    font-size: 1.3rem;
  }
  
  .test-page-content li {
    padding: 10px 12px;
  }
  
  .params-table th, .params-table td {
    padding: 8px 10px;
    font-size: 0.85rem;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.test-page-content > * {
  animation: fadeInUp 0.6s ease-out forwards;
}

.test-page-content > :nth-child(2) { animation-delay: 0.1s; }
.test-page-content > :nth-child(3) { animation-delay: 0.2s; }
.test-page-content > :nth-child(4) { animation-delay: 0.3s; }
.test-page-content > :nth-child(5) { animation-delay: 0.4s; }
.test-page-content > :nth-child(6) { animation-delay: 0.5s; }
.test-page-content > :nth-child(7) { animation-delay: 0.6s; }
.test-page-content > :nth-child(8) { animation-delay: 0.7s; }

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #bdc3c7;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #95a5a6;
}