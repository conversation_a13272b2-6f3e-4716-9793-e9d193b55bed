<?php

/**
 * 路由配置文件
 * 定义应用程序的路由规则
 */

return [
    // 默认路由
    'default' => 'home',
    
    // 路由规则
    'routes' => [
        // GET请求路由
        'GET' => [
            '/' => 'home',
            // 带参数的测试页面路由
            '/test' => 'test',
            '/test/{id}' => 'test',
            '/test/{id}/{slug}' => 'test',
     
        ],
        
        // POST请求路由
        'POST' => [
        ],
        
        // PUT请求路由
        'PUT' => [
        ],
        
        // DELETE请求路由
        'DELETE' => [
        ],
    ],
    
    // 404页面
    'not_found' => '404',
    
    // 启用调试模式
    'debug_mode' => true,
];