/* 主布局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #333;
}

/* 导航栏样式 */
header {
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

/* 自定义导航栏样式（用于core/layouts/main.php） */
nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

/* Bootstrap导航栏通栏背景样式（用于app/layouts/main.php） */
.navbar {
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    box-shadow: 0 2px 4px rgba(0,0,0,.1);
}

.navbar .container {
    max-width: 1200px;
    margin: 0 auto;
}

.nav-brand a {
    font-size: 1.5rem;
    font-weight: bold;
    text-decoration: none;
    color: #007bff;
}

.nav-menu ul {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-menu li {
    margin-left: 1.5rem;
}

.nav-menu a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s;
}

.nav-menu a:hover {
    color: #007bff;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger {
    width: 25px;
    height: 3px;
    background-color: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* 主要内容区域 */
main {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 2rem;
    min-height: calc(100vh - 120px);
}

/* 页脚样式 */
footer {
    background-color: #f8f9fa;
    padding: 1rem;
    text-align: center;
    margin-top: 2rem;
}

.footer-content p {
    margin: 0;
    color: #6c757d;
}

/* 响应式设计 */
@media (max-width: 768px) {
    nav {
        padding: 1rem;
    }

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: #fff;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0,0,0,.05);
        padding: 2rem 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-menu ul {
        flex-direction: column;
    }

    .nav-menu li {
        margin: 1rem 0;
    }

    .nav-toggle {
        display: flex;
    }

    .hamburger.active:nth-child(1) {
        transform: rotate(-45deg) translate(-5px, 6px);
    }

    .hamburger.active:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active:nth-child(3) {
        transform: rotate(45deg) translate(-5px, -6px);
    }

    main {
        padding: 0 1rem;
        margin: 1rem auto;
    }
}