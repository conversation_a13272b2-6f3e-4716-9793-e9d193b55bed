<?php
// 查询构建器

namespace Database;

class QueryBuilder
{
    private $connection;
    private $table;
    private $columns = ['*'];
    private $conditions = [];
    private $orderBy = [];
    private $limit;
    private $offset;
    
    public function __construct()
    {
        $this->connection = Connection::getInstance();
    }
    
    // 设置表名
    public function table($table)
    {
        $this->table = $this->connection->getTablePrefix() . $table;
        return $this;
    }
    
    // 设置查询字段
    public function select($columns)
    {
        $this->columns = is_array($columns) ? $columns : func_get_args();
        return $this;
    }
    
    // 添加WHERE条件
    public function where($column, $operator = null, $value = null)
    {
        if ($value === null) {
            $value = $operator;
            $operator = '=';
        }
        
        $this->conditions[] = [
            'column' => $column,
            'operator' => $operator,
            'value' => $value,
            'boolean' => 'AND'
        ];
        
        return $this;
    }
    
    // 添加OR WHERE条件
    public function orWhere($column, $operator = null, $value = null)
    {
        if ($value === null) {
            $value = $operator;
            $operator = '=';
        }
        
        $this->conditions[] = [
            'column' => $column,
            'operator' => $operator,
            'value' => $value,
            'boolean' => 'OR'
        ];
        
        return $this;
    }
    
    // ORDER BY子句
    public function orderBy($column, $direction = 'ASC')
    {
        $this->orderBy[] = [
            'column' => $column,
            'direction' => strtoupper($direction)
        ];
        
        return $this;
    }
    
    // LIMIT子句
    public function limit($limit)
    {
        $this->limit = $limit;
        return $this;
    }
    
    // OFFSET子句
    public function offset($offset)
    {
        $this->offset = $offset;
        return $this;
    }
    
    // 执行查询并获取所有结果
    public function get()
    {
        $sql = $this->buildSelectQuery();
        $params = $this->getParams();
        
        return $this->connection->query($sql, $params);
    }
    
    // 获取单条记录
    public function first()
    {
        $result = $this->limit(1)->get();
        return isset($result[0]) ? $result[0] : null;
    }
    
    // 根据ID查找记录
    public function find($id)
    {
        return $this->where('id', $id)->first();
    }
    
    // 插入数据
    public function insert($data)
    {
        $columns = array_keys($data);
        $values = array_values($data);
        
        $sql = "INSERT INTO {$this->table} (" . implode(', ', $columns) . ") VALUES (" . 
               implode(', ', array_fill(0, count($columns), '?')) . ")";
        
        $result = $this->connection->execute($sql, $values);
        
        if ($result) {
            return $this->connection->lastInsertId();
        }
        
        return false;
    }
    
    // 更新数据
    public function update($data)
    {
        $columns = array_keys($data);
        $values = array_values($data);
        
        $setClause = implode(', ', array_map(function($column) {
            return "{$column} = ?";
        }, $columns));
        
        $sql = "UPDATE {$this->table} SET {$setClause}";
        
        if (!empty($this->conditions)) {
            $sql .= " WHERE " . $this->buildWhereClause();
            $values = array_merge($values, $this->getWhereParams());
        }
        
        return $this->connection->execute($sql, $values);
    }
    
    // 删除数据
    public function delete()
    {
        $sql = "DELETE FROM {$this->table}";
        
        if (!empty($this->conditions)) {
            $sql .= " WHERE " . $this->buildWhereClause();
            $params = $this->getWhereParams();
            return $this->connection->execute($sql, $params);
        }
        
        return $this->connection->execute($sql);
    }
    
    // 构建SELECT查询
    private function buildSelectQuery()
    {
        $sql = "SELECT " . implode(', ', $this->columns) . " FROM {$this->table}";
        
        if (!empty($this->conditions)) {
            $sql .= " WHERE " . $this->buildWhereClause();
        }
        
        if (!empty($this->orderBy)) {
            $orderParts = [];
            foreach ($this->orderBy as $order) {
                $orderParts[] = "{$order['column']} {$order['direction']}";
            }
            $sql .= " ORDER BY " . implode(', ', $orderParts);
        }
        
        if ($this->limit !== null) {
            $sql .= " LIMIT {$this->limit}";
        }
        
        if ($this->offset !== null) {
            $sql .= " OFFSET {$this->offset}";
        }
        
        return $sql;
    }
    
    // 构建WHERE子句
    private function buildWhereClause()
    {
        $whereParts = [];
        foreach ($this->conditions as $condition) {
            $whereParts[] = "{$condition['column']} {$condition['operator']} ?";
        }
        return implode(" {$this->conditions[0]['boolean']} ", $whereParts);
    }
    
    // 获取查询参数
    private function getParams()
    {
        $params = [];
        foreach ($this->conditions as $condition) {
            $params[] = $condition['value'];
        }
        return $params;
    }
    
    // 获取WHERE参数
    private function getWhereParams()
    {
        $params = [];
        foreach ($this->conditions as $condition) {
            $params[] = $condition['value'];
        }
        return $params;
    }
}