/* 主样式表 */

/* 重置样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* 头部样式 */
.site-header {
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 1rem 0;
}

.site-header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.site-title a {
    text-decoration: none;
    color: #2c3e50;
    font-size: 1.5rem;
    font-weight: bold;
}

.site-nav ul {
    display: flex;
    list-style: none;
}

.site-nav ul li {
    margin-left: 1.5rem;
}

.site-nav ul li a {
    text-decoration: none;
    color: #34495e;
    font-weight: 500;
    transition: color 0.3s;
}

.site-nav ul li a:hover {
    color: #3498db;
}

/* 移动端导航菜单切换按钮 */
.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #34495e;
}

.mobile-hidden {
    display: block;
}

/* 主要内容区域 */
.main-content {
    min-height: calc(100vh - 150px);
    padding: 2rem 0;
}

/* 页脚样式 */
.site-footer {
    background-color: #2c3e50;
    color: #ecf0f1;
    text-align: center;
    padding: 1.5rem 0;
    margin-top: 2rem;
}

/* 通用样式 */
h1, h2, h3, h4, h5, h6 {
    margin-bottom: 1rem;
    color: #2c3e50;
}

p {
    margin-bottom: 1rem;
}

ul {
    margin-bottom: 1rem;
    padding-left: 1.5rem;
}

a {
    color: #3498db;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

/* 表单样式 */
.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

button {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

button:hover {
    background-color: #2980b9;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .site-header .container {
        flex-direction: column;
    }
    
    .site-nav {
        margin-top: 1rem;
    }
    
    .site-nav ul {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .site-nav ul li {
        margin: 0.5rem 1rem;
    }
    
    /* 移动端导航 */
    .mobile-menu-toggle {
        display: block;
        position: absolute;
        top: 1rem;
        right: 1rem;
    }
    
    .site-nav.mobile-hidden {
        display: none;
    }
    
    .site-nav:not(.mobile-hidden) {
        display: block;
    }
}