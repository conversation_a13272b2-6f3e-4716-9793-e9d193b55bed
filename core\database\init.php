<?php
// 数据库初始化脚本

// 获取数据库连接
$db = \Database\Connection::getInstance();

// 创建用户表
$sql = "
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    phone VARCHAR(20),
    address TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
)";

$db->execute($sql);

// 检查是否已有数据
$count = $db->query("SELECT COUNT(*) as count FROM users")[0]['count'];

if ($count == 0) {
    // 生成50条假数据
    $faker = new FakeDataGenerator();
    $users = $faker->generateUsers(50);
    
    foreach ($users as $user) {
        $db->execute(
            "INSERT INTO users (name, email, phone, address, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?)",
            [
                $user['name'],
                $user['email'],
                $user['phone'],
                $user['address'],
                $user['created_at'],
                $user['updated_at']
            ]
        );
    }
    
    echo "成功插入50条假用户数据\n";
} else {
    echo "用户表中已有数据，无需插入\n";
}

class FakeDataGenerator
{
    private $names = [
        '张伟', '王芳', '李娜', '刘强', '陈杰', '杨丽', '黄勇', '吴娟', '赵磊', '周琴',
        '徐洋', '孙燕', '胡明', '朱霞', '高飞', '林玲', '何东', '郭涛', '马丽', '罗强',
        '梁军', '宋霞', '郑伟', '谢芳', '韩波', '冯娟', '邓超', '曹艳', '彭亮', '曾丽',
        '肖勇', '田敏', '董强', '袁芳', '潘磊', '于丽', '蒋军', '蔡霞', '余洋', '杜娟',
        '丁强', '程燕', '苏勇', '卢敏', '蔡丽', '贾强', '侯芳', '邵勇', '孟敏', '龙强'
    ];
    
    private $domains = [
        'gmail.com', 'yahoo.com', 'hotmail.com', '163.com', '126.com', 'qq.com', 'sina.com', 'sohu.com'
    ];
    
    private $cities = [
        '北京市', '上海市', '广州市', '深圳市', '杭州市', '南京市', '成都市', '武汉市',
        '西安市', '重庆市', '天津市', '苏州市', '青岛市', '厦门市', '无锡市', '长沙市'
    ];
    
    public function generateUsers($count)
    {
        $users = [];
        for ($i = 0; $i < $count; $i++) {
            $users[] = [
                'name' => $this->names[array_rand($this->names)],
                'email' => $this->generateEmail(),
                'phone' => $this->generatePhone(),
                'address' => $this->generateAddress(),
                'created_at' => date('Y-m-d H:i:s', strtotime('-' . rand(1, 365) . ' days')),
                'updated_at' => date('Y-m-d H:i:s', strtotime('-' . rand(0, 30) . ' days'))
            ];
        }
        return $users;
    }
    
    private function generateEmail()
    {
        $name = strtolower($this->names[array_rand($this->names)]);
        $number = rand(1000, 9999);
        $domain = $this->domains[array_rand($this->domains)];
        return $name . $number . '@' . $domain;
    }
    
    private function generatePhone()
    {
        $prefixes = ['138', '139', '158', '159', '188', '189', '177', '166'];
        $prefix = $prefixes[array_rand($prefixes)];
        $suffix = str_pad(rand(0, 99999999), 8, '0', STR_PAD_LEFT);
        return $prefix . $suffix;
    }
    
    private function generateAddress()
    {
        $city = $this->cities[array_rand($this->cities)];
        $districts = ['朝阳区', '海淀区', '浦东新区', '天河区', '福田区', '鼓楼区', '历下区', '金水区'];
        $district = $districts[array_rand($districts)];
        $street = ['中山路', '解放路', '人民路', '建设路', '和平路', '青年路', '胜利路', '光明路'];
        $streetName = $street[array_rand($street)];
        $number = rand(1, 500);
        return $city . $district . $streetName . $number . '号';
    }
}