/**
 * 参数处理测试页面JS脚本
 * 遵循AiPHP框架脚本规范
 * 与绿色布局兼容
 */

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 添加参数链接的点击事件
    function setupParamLinks() {
        const paramLinks = document.querySelectorAll('.param-test-content a');
        
        paramLinks.forEach(link => {
            // 添加悬停效果
            link.addEventListener('mouseenter', function() {
                this.style.transform = 'translateX(5px)';
                this.style.boxShadow = '0 4px 8px rgba(52, 152, 219, 0.3)';
            });
            
            link.addEventListener('mouseleave', function() {
                this.style.transform = 'translateX(0)';
                this.style.boxShadow = 'none';
            });
            
            // 添加点击效果
            link.addEventListener('click', function(e) {
                // 添加点击动画效果
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
                
                // 向控制台输出点击信息
                console.log('点击了参数链接:', this.href);
            });
        });
    }
    
    // 添加表格行悬停效果
    function setupTableHover() {
        const tableRows = document.querySelectorAll('.params-table tbody tr');
        
        tableRows.forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.style.backgroundColor = '#edf7ff';
            });
            
            row.addEventListener('mouseleave', function() {
                this.style.backgroundColor = '';
            });
        });
    }
    
    // 添加复制功能到参数表格
    function setupCopyFunctionality() {
        const tables = document.querySelectorAll('.params-table');
        
        tables.forEach(table => {
            table.addEventListener('click', function(e) {
                if (e.target.tagName === 'TD') {
                    const text = e.target.textContent;
                    navigator.clipboard.writeText(text).then(() => {
                        // 显示复制成功的提示
                        const originalText = e.target.textContent;
                        e.target.textContent = '✓ 已复制';
                        e.target.style.color = '#27ae60';
                        e.target.style.fontWeight = 'bold';
                        
                        setTimeout(() => {
                            e.target.textContent = originalText;
                            e.target.style.color = '';
                            e.target.style.fontWeight = '';
                        }, 1500);
                    }).catch(err => {
                        console.error('复制失败:', err);
                    });
                }
            });
        });
    }
    
    // 添加页面加载完成的提示
    function showLoadIndicator() {
        const firstH2 = document.querySelector('h2');
        if (firstH2) {
            const loadIndicator = document.createElement('span');
            loadIndicator.textContent = ' ✓ 页面加载完成';
            loadIndicator.style.color = '#27ae60';
            loadIndicator.style.fontSize = '0.8em';
            loadIndicator.style.marginLeft = '10px';
            loadIndicator.style.opacity = '0';
            loadIndicator.style.transition = 'opacity 1s ease';
            firstH2.appendChild(loadIndicator);
            
            // 延迟显示提示
            setTimeout(() => {
                loadIndicator.style.opacity = '1';
            }, 500);
            
            // 2秒后淡出提示
            setTimeout(() => {
                loadIndicator.style.opacity = '0';
            }, 2000);
        }
    }
    
    // 初始化函数
    function init() {
        setupParamLinks();
        setupTableHover();
        setupCopyFunctionality();
        showLoadIndicator();
        
        // 向控制台输出初始化完成信息
        console.log('参数处理测试页面JS脚本初始化完成');
    }
    
    // 调用初始化函数
    init();
});