<?php

declare(strict_types=1);

/**
 * 验证错误信息测试
 */

// 包含必要的文件
require_once __DIR__ . '/../core/own-library/autoloader/autoloader.php';

// 注册自动加载器
use Core\OwnLibrary\Autoloader\Autoloader;

$autoloader = new Autoloader();
$autoloader->addNamespace('Core\OwnLibrary', __DIR__ . '/../core/own-library');
$autoloader->register();

// 使用命名空间
use Core\OwnLibrary\Validation\ValidationHelper;

// 测试参数
$params = ['id' => '顶顶顶顶'];

// 使用ValidationHelper验证
$result = ValidationHelper::validate($params, 'id', ['数字']);

// 输出结果
echo "验证结果:\n";
var_dump($result);

echo "\n错误信息:\n";
echo $result['id'] ?? "无错误";

echo "\n\n";

// 测试自定义错误信息
$result2 = ValidationHelper::validate($params, 'id', ['数字' => '必须是数字']);

echo "自定义错误信息验证结果:\n";
var_dump($result2);

echo "\n错误信息:\n";
echo $result2['id'] ?? "无错误";