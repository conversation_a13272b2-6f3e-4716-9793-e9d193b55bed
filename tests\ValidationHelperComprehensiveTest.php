<?php

declare(strict_types=1);

/**
 * ValidationHelper综合测试类
 * 
 * 测试ValidationHelper是否支持EnhancedParameterValidator的所有验证方法
 */

// 包含必要的文件
require_once __DIR__ . '/../core/own-library/autoloader/autoloader.php';

// 注册自动加载器
use Core\OwnLibrary\Autoloader\Autoloader;

$autoloader = new Autoloader();
$autoloader->addNamespace('Core\OwnLibrary', __DIR__ . '/../core/own-library');
$autoloader->register();

// 使用命名空间
use Core\OwnLibrary\Validation\ValidationHelper;

// 测试统计变量
$tests = 0;
$passed = 0;
$failed = 0;

// 测试函数定义
function runTest($name, $testFunction) {
    global $tests, $passed, $failed;
    
    $tests++;
    try {
        $result = $testFunction();
        if ($result) {
            $passed++;
            echo "✓ $name\n";
        } else {
            $failed++;
            echo "✗ $name\n";
        }
    } catch (Exception $e) {
        $failed++;
        echo "✗ $name - Exception: " . $e->getMessage() . "\n";
    }
}

// 测试必填验证
function testRequiredValidation() {
    $params = [];
    $result = ValidationHelper::validate($params, 'id', ['必填' => 'ID必须填写']);
    return isset($result['id']) && $result['id'] === 'ID必须填写';
}

// 测试类型验证 - 数字
function testTypeNumberValidation() {
    $params = ['id' => '123'];
    $result = ValidationHelper::validate($params, 'id', ['数字']);
    return empty($result);
}

// 测试类型验证 - 字符串
function testTypeStringValidation() {
    $params = ['name' => 'test'];
    $result = ValidationHelper::validate($params, 'name', ['字符串']);
    return empty($result);
}

// 测试格式验证 - 邮箱
function testFormatEmailValidation() {
    $params = ['email' => '<EMAIL>'];
    $result = ValidationHelper::validate($params, 'email', ['邮箱']);
    return empty($result);
}

// 测试格式验证 - URL
function testFormatUrlValidation() {
    $params = ['website' => 'https://example.com'];
    $result = ValidationHelper::validate($params, 'website', ['网址']);
    return empty($result);
}

// 测试格式验证 - 手机号
function testFormatPhoneValidation() {
    $params = ['phone' => '13812345678'];
    $result = ValidationHelper::validate($params, 'phone', ['手机']);
    return empty($result);
}

// 测试长度验证
function testLengthValidation() {
    $params = ['code' => '123456'];
    $result = ValidationHelper::validate($params, 'code', ['长度6']);
    return empty($result);
}

// 测试范围验证
function testRangeValidation() {
    $params = ['age' => '25'];
    $result = ValidationHelper::validate($params, 'age', ['范围18-65']);
    return empty($result);
}

// 测试枚举验证
function testEnumValidation() {
    $params = ['status' => 'active'];
    $result = ValidationHelper::validate($params, 'status', ['枚举(active,inactive,pending)']);
    return empty($result);
}

// 测试正则表达式验证
function testRegexValidation() {
    $params = ['username' => 'test123'];
    $result = ValidationHelper::validate($params, 'username', ['正则(/^[a-zA-Z0-9]+$/)' ]);
    return empty($result);
}

// 测试最小长度验证
function testMinLengthValidation() {
    $params = ['password' => '123456'];
    $result = ValidationHelper::validate($params, 'password', ['最小长度6']);
    return empty($result);
}

// 测试最大长度验证
function testMaxLengthValidation() {
    $params = ['name' => 'test'];
    $result = ValidationHelper::validate($params, 'name', ['最大长度10']);
    return empty($result);
}

// 测试最小值验证
function testMinValueValidation() {
    $params = ['score' => '60'];
    $result = ValidationHelper::validate($params, 'score', ['最小值0']);
    return empty($result);
}

// 测试最大值验证
function testMaxValueValidation() {
    $params = ['score' => '100'];
    $result = ValidationHelper::validate($params, 'score', ['最大值100']);
    return empty($result);
}

// 测试IPv4验证
function testIPv4Validation() {
    $params = ['ip' => '***********'];
    $result = ValidationHelper::validate($params, 'ip', ['IPv4']);
    return empty($result);
}

// 测试日期验证
function testDateValidation() {
    $params = ['date' => '2023-01-01'];
    $result = ValidationHelper::validate($params, 'date', ['日期']);
    return empty($result);
}

// 测试UUID验证
function testUUIDValidation() {
    $params = ['uuid' => '550e8400-e29b-41d4-a716-************'];
    $result = ValidationHelper::validate($params, 'uuid', ['UUID']);
    return empty($result);
}

// 执行测试
echo "开始测试ValidationHelper类对EnhancedParameterValidator所有方法的支持...\n\n";

runTest("必填验证", "testRequiredValidation");
runTest("类型验证 - 数字", "testTypeNumberValidation");
runTest("类型验证 - 字符串", "testTypeStringValidation");
runTest("格式验证 - 邮箱", "testFormatEmailValidation");
runTest("格式验证 - URL", "testFormatUrlValidation");
runTest("格式验证 - 手机号", "testFormatPhoneValidation");
runTest("长度验证", "testLengthValidation");
runTest("范围验证", "testRangeValidation");
runTest("枚举验证", "testEnumValidation");
runTest("正则表达式验证", "testRegexValidation");
runTest("最小长度验证", "testMinLengthValidation");
runTest("最大长度验证", "testMaxLengthValidation");
runTest("最小值验证", "testMinValueValidation");
runTest("最大值验证", "testMaxValueValidation");
runTest("IPv4验证", "testIPv4Validation");
runTest("日期验证", "testDateValidation");
runTest("UUID验证", "testUUIDValidation");

// 输出测试结果统计
echo "\n测试完成!\n";
echo "总测试数: $tests\n";
echo "通过: $passed\n";
echo "失败: $failed\n";
echo "通过率: " . ($tests > 0 ? round(($passed / $tests) * 100, 2) : 0) . "%\n";