# AiPHP 测试文件编写指南

## 概述
本指南详细说明了在AiPHP框架中编写测试文件的规范和最佳实践，确保测试代码的一致性、可维护性和有效性。

## 测试文件位置
- 所有测试文件必须放置在 `tests/` 目录下
- 测试文件名应遵循 `TestNameTest.php` 的命名约定，例如 `PageLoaderTest.php`

## 测试文件基本结构
测试文件应遵循以下基本结构：

```php
<?php

declare(strict_types=1);

/**
 * 测试文件描述
 */

// 包含必要的文件
require_once __DIR__ . '/../path/to/class/being/tested.php';

// 使用命名空间
use Namespace\Of\ClassBeingTested;

// 初始化测试对象
$testObject = new ClassBeingTested();

// 测试函数定义
function testFunctionality($testObject) {
    // 测试逻辑
}

// 执行测试
runTests();
```

## 测试方法要求

### 1. 简化原则
- 测试代码应保持简洁明了，专注于要测试的核心功能
- 避免不必要的复杂性和冗余代码
- 优先使用直接函数式测试而非复杂的类结构

### 2. 数据提取与使用
- 从配置文件中提取数据进行测试，特别是路由配置 `config/routes.php`
- 确保测试数据能够覆盖各种情况（有效路径、无效路径等）
- 对提取的数据进行去重处理，避免重复测试

### 3. 测试统计与汇报
- 实现测试统计功能，记录总测试数、通过/失败数量
- 提供详细的测试报告，包括：
  - 总测试路径数
  - 存在的页面数
  - 不存在的页面数
  - 存在率百分比
  - 分类列出存在和不存在的页面路径

### 4. 测试结果输出
- 清晰地输出每个测试项的结果，使用视觉标记（如 ✓ 和 ✗）表示通过或失败
- 显示完整的文件路径，便于定位问题
- 提供测试完成的明确指示

## 具体测试类型

### 页面加载器测试
- 测试构造函数初始化
- 验证根路径和基础目录设置
- 测试页面存在性验证功能
  - 有效路径测试
  - 无效路径测试
- 测试目录遍历防护机制
- 测试所有路由配置中的路径

### 目录遍历防护测试要点
- 测试包含 `../` 和 `..\` 的路径
- 验证系统能够正确清理路径并防止越界访问
- 确保防护机制不会影响正常路径的访问

## 最佳实践
- 每个测试函数应专注于测试一个特定功能
- 使用适当的注释说明测试目的和预期结果
- 确保测试代码与生产代码风格一致
- 在测试前清理环境，避免测试间的相互影响
- 测试完成后提供清晰的统计和汇报

## 执行测试
使用命令行执行测试文件：
```bash
php tests/TestFileNameTest.php
```

## 与主框架集成
- 测试文件应使用与主框架相同的编码标准和命名约定
- 确保测试能够正确加载框架的核心类和配置
- 测试结果应易于理解，便于开发人员快速定位问题