<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($title) ? htmlspecialchars($title) : '默认标题'; ?></title>
    <link rel="stylesheet" href="/static/third-party/bootstrap.min.css">
    <link rel="stylesheet" href="/static/css/main.css">
    <?php if (isset($pageName)): ?>
        <link rel="stylesheet" href="/static/css/<?php echo htmlspecialchars($pageName); ?>.css">
    <?php endif; ?>
</head>
<body>
<!-- 导航栏 -->
<div class="container">
    <nav class="navbar navbar-expand-lg navbar-light bg-light">
        <div class="container">
            <a class="navbar-brand" href="/">AI PHP</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                    aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/users">用户列表</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
</div>

<!-- 主要内容 -->
<div class="container">
    <main>
        <?php echo isset($content) ? $content : ''; ?>
    </main>
</div>
<div class="container">
    <!-- 页脚 -->
    <footer class="mt-5 py-4 bg-light">
        <div class="container">
            <p class="text-center mb-0">&copy; <?php echo date('Y'); ?> AI PHP. 保留所有权利。</p>
        </div>
    </footer>
    <div class="container">
        <script src="/static/third-party/jquery.min.js"></script>
        <script src="/static/third-party/bootstrap.bundle.min.js"></script>
        <script src="/static/js/main.js"></script>
        <?php if (isset($pageName)): ?>
            <script src="/static/js/<?php echo htmlspecialchars($pageName); ?>.js"></script>
        <?php endif; ?>
</body>
</html>