// 首页专用JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // 获取所有行动号召按钮
    const ctaButtons = document.querySelectorAll('.cta-button');
    
    // 为按钮添加点击事件
    ctaButtons.forEach(function(button) {
        button.addEventListener('click', function() {
            alert('感谢您的关注！这是一个演示页面。');
        });
    });
    
    // 添加页面加载动画
    const heroSection = document.querySelector('.hero');
    if (heroSection) {
        heroSection.style.opacity = '0';
        heroSection.style.transform = 'translateY(20px)';
        heroSection.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        
        setTimeout(function() {
            heroSection.style.opacity = '1';
            heroSection.style.transform = 'translateY(0)';
        }, 100);
    }
});