<?php

declare(strict_types=1);

/**
 * 所有验证规则测试脚本
 * 
 * 测试docs/validation_classes_usage.md文档中提到的所有验证规则是否正常工作
 */

// 包含必要的文件
require_once __DIR__ . '/../core/own-library/autoloader/autoloader.php';

// 注册自动加载器
use Core\OwnLibrary\Autoloader\Autoloader;

$autoloader = new Autoloader();
$autoloader->addNamespace('Core\OwnLibrary', __DIR__ . '/../core/own-library');
$autoloader->register();

// 使用命名空间
use Core\OwnLibrary\Validation\ValidationHelper;

// 测试统计变量
$tests = 0;
$passed = 0;
$failed = 0;

// 测试函数定义
function runTest($name, $testFunction) {
    global $tests, $passed, $failed;
    
    $tests++;
    try {
        $result = $testFunction();
        if ($result) {
            $passed++;
            echo "✓ $name\n";
        } else {
            $failed++;
            echo "✗ $name\n";
        }
    } catch (Exception $e) {
        $failed++;
        echo "✗ $name - Exception: " . $e->getMessage() . "\n";
    }
}

// 必填验证测试
function testRequiredValidation() {
    // 测试必填验证失败
    $params = [];
    $result = ValidationHelper::validate($params, 'id', ['必填' => 'ID必须填写']);
    $failResult = isset($result['id']);
    
    // 测试必填验证通过
    $params = ['id' => '123'];
    $result = ValidationHelper::validate($params, 'id', ['必填' => 'ID必须填写']);
    $passResult = empty($result);
    
    return $failResult && $passResult;
}

// 类型验证测试
function testTypeValidation() {
    // 数字验证
    $params = ['id' => '123'];
    $result = ValidationHelper::validate($params, 'id', ['数字']);
    $numberResult = empty($result);
    
    // 字符串验证
    $params = ['name' => 'test'];
    $result = ValidationHelper::validate($params, 'name', ['字符串']);
    $stringResult = empty($result);
    
    // 布尔值验证
    $params = ['active' => true];
    $result = ValidationHelper::validate($params, 'active', ['布尔值']);
    $boolResult = empty($result);
    
    // 浮点数验证
    $params = ['price' => 99.99];
    $result = ValidationHelper::validate($params, 'price', ['浮点数']);
    $floatResult = empty($result);
    
    // 数组验证
    $params = ['tags' => ['php', 'javascript']];
    $result = ValidationHelper::validate($params, 'tags', ['数组']);
    $arrayResult = empty($result);
    
    // 空值验证
    $params = ['optional' => null];
    $result = ValidationHelper::validate($params, 'optional', ['空值']);
    $nullResult = empty($result);
    
    return $numberResult && $stringResult && $boolResult && $floatResult && $arrayResult && $nullResult;
}

// 格式验证测试
function testFormatValidation() {
    // 邮箱验证
    $params = ['email' => '<EMAIL>'];
    $result = ValidationHelper::validate($params, 'email', ['邮箱']);
    $emailResult = empty($result);
    
    // 网址验证
    $params = ['website' => 'https://example.com'];
    $result = ValidationHelper::validate($params, 'website', ['网址']);
    $urlResult = empty($result);
    
    // 手机号验证
    $params = ['phone' => '13812345678'];
    $result = ValidationHelper::validate($params, 'phone', ['手机']);
    $phoneResult = empty($result);
    
    // IP地址验证
    $params = ['ip' => '***********'];
    $result = ValidationHelper::validate($params, 'ip', ['IP地址']);
    $ipResult = empty($result);
    
    // IPv4验证
    $params = ['ipv4' => '***********'];
    $result = ValidationHelper::validate($params, 'ipv4', ['IPv4']);
    $ipv4Result = empty($result);
    
    // IPv6验证
    $params = ['ipv6' => '2001:0db8:85a3:0000:0000:8a2e:0370:7334'];
    $result = ValidationHelper::validate($params, 'ipv6', ['IPv6']);
    $ipv6Result = empty($result);
    
    // 字母验证
    $params = ['code' => 'ABC'];
    $result = ValidationHelper::validate($params, 'code', ['字母']);
    $alphaResult = empty($result);
    
    // 字母数字验证
    $params = ['code' => 'ABC123'];
    $result = ValidationHelper::validate($params, 'code', ['字母数字']);
    $alnumResult = empty($result);
    
    // 数值验证
    $params = ['amount' => '123.45'];
    $result = ValidationHelper::validate($params, 'amount', ['数值']);
    $numericResult = empty($result);
    
    // 数字串验证
    $params = ['pin' => '123456'];
    $result = ValidationHelper::validate($params, 'pin', ['数字串']);
    $digitsResult = empty($result);
    
    // slug验证
    $params = ['slug' => 'test-post-123'];
    $result = ValidationHelper::validate($params, 'slug', ['slug']);
    $slugResult = empty($result);
    
    // 日期验证
    $params = ['date' => '2023-01-01'];
    $result = ValidationHelper::validate($params, 'date', ['日期']);
    $dateResult = empty($result);
    
    // 日期时间验证
    $params = ['datetime' => '2023-01-01 12:00:00'];
    $result = ValidationHelper::validate($params, 'datetime', ['日期时间']);
    $datetimeResult = empty($result);
    
    // UUID验证
    $params = ['uuid' => '550e8400-e29b-41d4-a716-************'];
    $result = ValidationHelper::validate($params, 'uuid', ['UUID']);
    $uuidResult = empty($result);
    
    // 信用卡验证
    $params = ['card' => '****************'];
    $result = ValidationHelper::validate($params, 'card', ['信用卡']);
    $creditCardResult = empty($result);
    
    // 邮政编码验证
    $params = ['zip' => '123456'];
    $result = ValidationHelper::validate($params, 'zip', ['邮政编码']);
    $postalCodeResult = empty($result);
    
    return $emailResult && $urlResult && $phoneResult && $ipResult && $ipv4Result && $ipv6Result && 
           $alphaResult && $alnumResult && $numericResult && $digitsResult && $slugResult && 
           $dateResult && $datetimeResult && $uuidResult && $creditCardResult && $postalCodeResult;
}

// 长度验证测试
function testLengthValidation() {
    // 固定长度验证
    $params = ['code' => '123456'];
    $result = ValidationHelper::validate($params, 'code', ['长度6']);
    $exactLengthResult = empty($result);
    
    // 长度范围验证
    $params = ['password' => '12345678'];
    $result = ValidationHelper::validate($params, 'password', ['长度6-12']);
    $rangeLengthResult = empty($result);
    
    // 最小长度验证
    $params = ['password' => '123456'];
    $result = ValidationHelper::validate($params, 'password', ['最小长度6']);
    $minLengthResult = empty($result);
    
    // 最大长度验证
    $params = ['name' => 'test'];
    $result = ValidationHelper::validate($params, 'name', ['最大长度10']);
    $maxLengthResult = empty($result);
    
    return $exactLengthResult && $rangeLengthResult && $minLengthResult && $maxLengthResult;
}

// 范围验证测试
function testRangeValidation() {
    // 数值范围验证
    $params = ['age' => '25'];
    $result = ValidationHelper::validate($params, 'age', ['范围18-65']);
    $rangeResult = empty($result);
    
    // 最小值验证
    $params = ['score' => '60'];
    $result = ValidationHelper::validate($params, 'score', ['最小值0']);
    $minValueResult = empty($result);
    
    // 最大值验证
    $params = ['score' => '100'];
    $result = ValidationHelper::validate($params, 'score', ['最大值100']);
    $maxValueResult = empty($result);
    
    return $rangeResult && $minValueResult && $maxValueResult;
}

// 枚举验证测试
function testEnumValidation() {
    $params = ['status' => 'active'];
    $result = ValidationHelper::validate($params, 'status', ['枚举(active,inactive,pending)']);
    return empty($result);
}

// 正则表达式验证测试
function testRegexValidation() {
    $params = ['username' => 'test123'];
    $result = ValidationHelper::validate($params, 'username', ['正则(/^[a-zA-Z0-9]+$/)' ]);
    return empty($result);
}

// 自定义错误信息测试
function testCustomErrorMessage() {
    // 必填验证自定义错误信息
    $params = [];
    $result = ValidationHelper::validate($params, 'id', ['必填' => '自定义必填错误']);
    $requiredCustom = isset($result['id']) && $result['id'] === '自定义必填错误';
    
    // 类型验证自定义错误信息
    $params = ['id' => 'abc'];
    $result = ValidationHelper::validate($params, 'id', ['数字' => '自定义数字错误']);
    $typeCustom = isset($result['id']) && $result['id'] === '自定义数字错误';
    
    // 长度验证自定义错误信息 - 修正：应该检查长度验证失败的情况
    $params = ['code' => '123'];
    $result = ValidationHelper::validate($params, 'code', ['长度6' => '自定义长度错误']);
    $lengthCustom = isset($result['code']) && $result['code'] === '自定义长度错误';
    
    return $requiredCustom && $typeCustom && $lengthCustom;
}

// 执行测试
echo "开始测试所有验证规则...\n\n";

runTest("必填验证", "testRequiredValidation");
runTest("类型验证", "testTypeValidation");
runTest("格式验证", "testFormatValidation");
runTest("长度验证", "testLengthValidation");
runTest("范围验证", "testRangeValidation");
runTest("枚举验证", "testEnumValidation");
runTest("正则表达式验证", "testRegexValidation");
runTest("自定义错误信息", "testCustomErrorMessage");

// 输出测试结果统计
echo "\n测试完成!\n";
echo "总测试数: $tests\n";
echo "通过: $passed\n";
echo "失败: $failed\n";
echo "通过率: " . ($tests > 0 ? round(($passed / $tests) * 100, 2) : 0) . "%\n";