<?php

declare(strict_types=1);

namespace Core\OwnLibrary\PageLoader;

/**
 * 页面加载器类
 * 
 * 该类专注于加载页面文件，遵循简单的加载逻辑：
 * 1. 如果请求的页面文件存在，则加载该文件
 * 2. 如果请求的页面文件不存在，则尝试加载404页面
 * 3. 如果404页面也不存在，则输出404错误信息
 * 
 * 该类不处理布局相关的功能，布局是页面文件自身的职责。
 */
class PageLoader
{
    /**
     * 网站根目录路径
     * 
     * 存储项目根目录的绝对路径，用于构建页面文件的完整路径。
     * 
     * @var string
     */
    private string $rootPath;
    
    /**
     * 页面文件总目录（相对于网站根目录）
     * 
     * 存储所有页面文件的基础路径，所有页面文件都应位于此目录下或其子目录中。
     * 默认目录为 'handler/pages'，符合AiPHP框架的目录结构规范。
     * 
     * @var string
     */
    private string $baseDirectory = 'handler/pages';

    /**
     * 构造函数
     * 
     * 初始化网站根目录路径。通过计算当前文件的上级目录来确定项目根目录，
     * 确保路径计算的准确性。
     */
    public function __construct() 
    {
        // 修正根目录计算方式，确保指向项目根目录
        // dirname(__DIR__, 3) 表示从当前文件向上三级目录即为项目根目录
        // core/own-library/pageloader/ -> core/own-library/ -> core/ -> 项目根目录
        $this->rootPath = dirname(__DIR__, 3);
    }

    /**
     * 设置页面文件总目录
     * 
     * 该方法用于设置所有页面文件的基础目录路径。所有页面文件都应位于此目录下或其子目录中。
     * 
     * @param string $directory 页面文件总目录路径（相对于网站根目录）
     */
    public function setBaseDirectory(string $directory): void
    {
        $this->baseDirectory = trim(str_replace('\\', '/', $directory), '/');
    }

    /**
     * 验证页面文件是否存在
     * 
     * 根据路由获取的文件路径和设置的总目录，构建完整的文件路径并验证文件是否存在。
     * 文件路径会自动添加 .php 扩展名进行检查。
     * 
     * @param string $filePath 从路由中获取的相对文件路径
     * @return bool 页面文件是否存在
     */
    public function pageExists(string $filePath): bool
    {
        $fullPath = $this->buildFilePath($filePath);
        return file_exists($fullPath);
    }

    /**
     * 加载页面文件
     * 
     * 遵循简单的加载逻辑：
     * 1. 先检查请求的页面文件是否存在，存在则加载
     * 2. 如果不存在，则尝试加载404页面
     * 3. 如果404页面也不存在，则输出404错误信息
     * 
     * @param string $filePath 从路由中获取的相对文件路径
     * @param array $params 路由参数
     */
    public function loadPage(string $filePath, array $params = []): void
    {
        
        // 将参数提取到当前作用域，使它们在页面文件中可用
        extract($params);
        
        $fullPath = $this->buildFilePath($filePath);
        
        if (file_exists($fullPath)) {
            // 文件存在，直接加载
            require $fullPath;
            return;
        }
        
        // 尝试加载404页面
        $notFoundPath = $this->buildFilePath('404');
        if (file_exists($notFoundPath)) {
            require $notFoundPath;
            return;
        }
        
        // 输出404错误信息
        $this->showNotFoundError($filePath);
    }

    /**
     * 显示404错误信息
     * 
     * 当请求的页面和404页面都不存在时，显示基础的404错误信息。
     * 
     * @param string $requestedPath 请求的路径
     */
    private function showNotFoundError(string $requestedPath): void
    {
        // 设置HTTP状态码为404
        http_response_code(404);
        
        // 输出基础的错误信息
        echo "<!DOCTYPE html>\n";
        echo "<html lang='zh-CN'>\n";
        echo "<head>\n";
        echo "    <meta charset='UTF-8'>\n";
        echo "    <meta name='viewport' content='width=device-width, initial-scale=1.0'>\n";
        echo "    <title>页面未找到 - 404</title>\n";
        echo "</head>\n";
        echo "<body>\n";
        echo "    <h1>404 - 页面未找到</h1>\n";
        echo "    <p>请求的页面不存在: '{$requestedPath}'</p>\n";
        echo "</body>\n";
        echo "</html>\n";
    }

    /**
     * 构建完整的页面文件路径
     * 
     * 将设置的总目录和路由获取的文件路径组合成完整的文件路径，并添加 .php 扩展名。
     * 同时清理路径中的相对路径符号，防止目录遍历攻击。
     * 
     * @param string $filePath 从路由中获取的相对文件路径
     * @return string 完整的页面文件路径（绝对路径）
     */
    private function buildFilePath(string $filePath): string
    {
        // 清理路径中的相对路径符号，防止目录遍历攻击
        $cleanPath = str_replace(['../', '..\\'], '', $filePath);
        $cleanPath = ltrim(str_replace('\\', '/', $cleanPath), '/');
        
        // 构建完整路径
        $path = $this->rootPath . DIRECTORY_SEPARATOR . $this->baseDirectory . DIRECTORY_SEPARATOR . $cleanPath . '.php';
        
        // 规范化路径分隔符
        return str_replace('/', DIRECTORY_SEPARATOR, $path);
    }

    /**
     * 获取当前设置的页面文件总目录
     * 
     * @return string 页面文件总目录路径
     */
    public function getBaseDirectory(): string
    {
        return $this->baseDirectory;
    }
    
    /**
     * 获取网站根目录路径
     * 
     * @return string 网站根目录绝对路径
     */
    public function getRootPath(): string 
    {
        return $this->rootPath;
    }
}