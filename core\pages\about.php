<?php
// 关于页面

// 页面标题
$title = '关于 AiPHP';

// 页面文件名（不含.php扩展名）
$pageName = 'about';

// 页面内容
ob_start();
?>
<div class="about-hero">
    <h1>关于 AiPHP 框架</h1>
    <p class="hero-subtitle">现代化、智能化的PHP开发框架</p>
</div>

<div class="about-content">
    <section class="framework-intro">
        <h2>框架介绍</h2>
        <div class="intro-grid">
            <div class="intro-item">
                <h3>🚀 高性能</h3>
                <p>AiPHP采用轻量级架构设计，优化的路由系统和缓存机制，确保应用程序快速响应。</p>
            </div>
            <div class="intro-item">
                <h3>🤖 AI驱动</h3>
                <p>集成人工智能辅助开发功能，智能代码生成、自动化测试和性能优化建议。</p>
            </div>
            <div class="intro-item">
                <h3>📱 响应式</h3>
                <p>内置响应式设计组件，支持多设备适配，让您的应用在任何设备上都表现完美。</p>
            </div>
            <div class="intro-item">
                <h3>🔒 安全可靠</h3>
                <p>内置安全防护机制，包括SQL注入防护、XSS防护和CSRF令牌验证。</p>
            </div>
            <div class="intro-item">
                <h3>⚡ 快速开发</h3>
                <p>简洁的MVC架构，丰富的开发工具和详细的文档，让开发变得更加高效。</p>
            </div>
            <div class="intro-item">
                <h3>🔧 易于扩展</h3>
                <p>模块化设计，支持插件系统，可以轻松扩展功能和集成第三方服务。</p>
            </div>
        </div>
    </section>

    <section class="framework-comparison">
        <h2>框架对比</h2>
        <p class="comparison-intro">AiPHP与主流PHP框架的详细对比分析</p>
        
        <div class="comparison-table-container">
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>特性</th>
                        <th class="highlight">AiPHP</th>
                        <th>Laravel</th>
                        <th>Symfony</th>
                        <th>CodeIgniter</th>
                        <th>Phalcon</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>学习曲线</strong></td>
                        <td class="highlight">⭐⭐⭐⭐⭐ 简单</td>
                        <td>⭐⭐⭐ 中等</td>
                        <td>⭐⭐ 复杂</td>
                        <td>⭐⭐⭐⭐ 简单</td>
                        <td>⭐⭐⭐ 中等</td>
                    </tr>
                    <tr>
                        <td><strong>性能</strong></td>
                        <td class="highlight">⭐⭐⭐⭐⭐ 优秀</td>
                        <td>⭐⭐⭐ 良好</td>
                        <td>⭐⭐⭐⭐ 很好</td>
                        <td>⭐⭐⭐⭐ 很好</td>
                        <td>⭐⭐⭐⭐⭐ 优秀</td>
                    </tr>
                    <tr>
                        <td><strong>AI集成</strong></td>
                        <td class="highlight">⭐⭐⭐⭐⭐ 原生支持</td>
                        <td>⭐⭐ 需要扩展</td>
                        <td>⭐⭐ 需要扩展</td>
                        <td>⭐ 需要自定义</td>
                        <td>⭐⭐ 需要扩展</td>
                    </tr>
                    <tr>
                        <td><strong>文档质量</strong></td>
                        <td class="highlight">⭐⭐⭐⭐⭐ 详细</td>
                        <td>⭐⭐⭐⭐⭐ 详细</td>
                        <td>⭐⭐⭐⭐ 很好</td>
                        <td>⭐⭐⭐⭐ 很好</td>
                        <td>⭐⭐⭐ 一般</td>
                    </tr>
                    <tr>
                        <td><strong>社区支持</strong></td>
                        <td class="highlight">⭐⭐⭐ 成长中</td>
                        <td>⭐⭐⭐⭐⭐ 活跃</td>
                        <td>⭐⭐⭐⭐⭐ 活跃</td>
                        <td>⭐⭐⭐⭐ 稳定</td>
                        <td>⭐⭐⭐ 一般</td>
                    </tr>
                    <tr>
                        <td><strong>开发速度</strong></td>
                        <td class="highlight">⭐⭐⭐⭐⭐ 快速</td>
                        <td>⭐⭐⭐⭐ 很快</td>
                        <td>⭐⭐⭐ 中等</td>
                        <td>⭐⭐⭐⭐ 很快</td>
                        <td>⭐⭐⭐⭐ 很快</td>
                    </tr>
                    <tr>
                        <td><strong>内存占用</strong></td>
                        <td class="highlight">⭐⭐⭐⭐⭐ 低</td>
                        <td>⭐⭐⭐ 中等</td>
                        <td>⭐⭐ 较高</td>
                        <td>⭐⭐⭐⭐⭐ 低</td>
                        <td>⭐⭐⭐⭐⭐ 低</td>
                    </tr>
                    <tr>
                        <td><strong>扩展性</strong></td>
                        <td class="highlight">⭐⭐⭐⭐⭐ 优秀</td>
                        <td>⭐⭐⭐⭐⭐ 优秀</td>
                        <td>⭐⭐⭐⭐⭐ 优秀</td>
                        <td>⭐⭐⭐ 一般</td>
                        <td>⭐⭐⭐⭐ 很好</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </section>

    <section class="why-choose">
        <h2>为什么选择 AiPHP？</h2>
        <div class="reasons-grid">
            <div class="reason-card">
                <div class="reason-icon">🎯</div>
                <h3>专注开发效率</h3>
                <p>AI辅助开发，智能代码提示，自动化测试生成，让开发者专注于业务逻辑而非重复工作。</p>
            </div>
            <div class="reason-card">
                <div class="reason-icon">🌟</div>
                <h3>现代化架构</h3>
                <p>采用最新的PHP特性和设计模式，支持容器化部署，微服务架构，适应现代开发需求。</p>
            </div>
            <div class="reason-card">
                <div class="reason-icon">💡</div>
                <h3>智能优化</h3>
                <p>内置性能监控和优化建议，AI分析代码质量，自动识别性能瓶颈并提供解决方案。</p>
            </div>
            <div class="reason-card">
                <div class="reason-icon">🔄</div>
                <h3>持续更新</h3>
                <p>活跃的开发团队，定期更新和安全补丁，紧跟PHP生态系统的最新发展。</p>
            </div>
        </div>
    </section>

    <section class="get-started">
        <h2>开始使用 AiPHP</h2>
        <p>准备好体验下一代PHP开发框架了吗？</p>
        <div class="cta-buttons">
            <a href="#" class="btn btn-primary">查看文档</a>
            <a href="#" class="btn btn-secondary">下载框架</a>
            <a href="/contact" class="btn btn-outline">联系我们</a>
        </div>
    </section>
</div>
<?php
$content = ob_get_clean();

// 引入布局模板
require_once ROOT_PATH . '/core/layouts/main.php';
