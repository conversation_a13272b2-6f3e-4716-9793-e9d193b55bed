<?php
$title = 'AIPHP框架简介';
$pageName = 'about';

ob_start();
?>

<div class="about-container">
    <section class="hero-section">
        <h1>AIPHP框架</h1>
        <p class="subtitle">下一代智能PHP开发框架</p>
    </section>

    <section class="features-section">
        <h2>框架特点</h2>
        <div class="features-grid">
            <div class="feature-item">
                <h3>AI驱动开发</h3>
                <p>集成先进的AI辅助功能,提供智能代码补全、自动化测试生成和性能优化建议。</p>
            </div>
            <div class="feature-item">
                <h3>简洁优雅</h3>
                <p>遵循现代PHP设计理念,提供清晰的项目结构和直观的API设计。</p>
            </div>
            <div class="feature-item">
                <h3>高性能</h3>
                <p>采用轻量级架构设计,内置缓存优化,响应速度快。</p>
            </div>
            <div class="feature-item">
                <h3>易于使用</h3>
                <p>详尽的中文文档,友好的开发体验,快速上手。</p>
            </div>
        </div>
    </section>

    <section class="comparison-section">
        <h2>框架对比</h2>
        <div class="comparison-table">
            <table>
                <thead>
                    <tr>
                        <th>特性</th>
                        <th>AIPHP</th>
                        <th>Laravel</th>
                        <th>ThinkPHP</th>
                        <th>Yii</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>AI辅助开发</td>
                        <td>✓</td>
                        <td>✗</td>
                        <td>✗</td>
                        <td>✗</td>
                    </tr>
                    <tr>
                        <td>中文优先</td>
                        <td>✓</td>
                        <td>✗</td>
                        <td>✓</td>
                        <td>部分</td>
                    </tr>
                    <tr>
                        <td>学习曲线</td>
                        <td>平缓</td>
                        <td>陡峭</td>
                        <td>中等</td>
                        <td>中等</td>
                    </tr>
                    <tr>
                        <td>性能表现</td>
                        <td>极佳</td>
                        <td>良好</td>
                        <td>良好</td>
                        <td>良好</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </section>

    <section class="why-choose-section">
        <h2>为什么选择AIPHP?</h2>
        <div class="advantages-list">
            <div class="advantage-item">
                <h3>面向未来</h3>
                <p>集成AI技术,引领PHP开发新范式。</p>
            </div>
            <div class="advantage-item">
                <h3>开发效率</h3>
                <p>智能工具链显著提升开发效率。</p>
            </div>
            <div class="advantage-item">
                <h3>中国优先</h3>
                <p>专注本土开发场景,提供完整中文支持。</p>
            </div>
        </div>
    </section>
</div>

<?php
$content = ob_get_clean();
require_once __DIR__ . '/../layouts/main.php';
?>
