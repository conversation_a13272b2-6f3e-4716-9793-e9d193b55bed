<?php
// 数据库连接管理器

namespace Database;

class Connection
{
    // 单例实例
    private static $instance = null;
    
    // 数据库适配器
    private $adapter = null;
    
    // 配置信息
    private $config = [];
    
    // 构造函数私有化，实现单例模式
    private function __construct()
    {
        $this->config = require __DIR__ . '/config.php';
        $this->connect();
    }
    
    // 获取单例实例
    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        
        return self::$instance;
    }
    
    // 建立数据库连接
    private function connect()
    {
        $defaultConnection = $this->config['default'];
        $connectionConfig = $this->config['connections'][$defaultConnection];
        
        switch ($connectionConfig['driver']) {
            case 'sqlite':
                $this->adapter = new SQLiteAdapter($connectionConfig);
                break;
            case 'mysql':
                $this->adapter = new MySQLAdapter($connectionConfig);
                break;
            default:
                throw new \Exception("不支持的数据库驱动: " . $connectionConfig['driver']);
        }
    }
    
    // 执行查询
    public function query($sql, $params = [])
    {
        return $this->adapter->query($sql, $params);
    }
    
    // 执行非查询SQL（插入、更新、删除）
    public function execute($sql, $params = [])
    {
        return $this->adapter->execute($sql, $params);
    }
    
    // 获取最后插入的ID
    public function lastInsertId()
    {
        return $this->adapter->lastInsertId();
    }
    
    // 开始事务
    public function beginTransaction()
    {
        return $this->adapter->beginTransaction();
    }
    
    // 提交事务
    public function commit()
    {
        return $this->adapter->commit();
    }
    
    // 回滚事务
    public function rollback()
    {
        return $this->adapter->rollback();
    }
    
    // 获取表前缀
    public function getTablePrefix()
    {
        $defaultConnection = $this->config['default'];
        return $this->config['connections'][$defaultConnection]['prefix'];
    }
}