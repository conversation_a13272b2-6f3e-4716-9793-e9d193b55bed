<?php
// 引导文件

// 定义项目根目录常量
define('ROOT_PATH', dirname(__DIR__));

// 硬编码加载数据库类
require_once ROOT_PATH . '/core/database/Connection.php';
require_once ROOT_PATH . '/core/database/SQLiteAdapter.php';
require_once ROOT_PATH . '/core/database/MySQLAdapter.php';
require_once ROOT_PATH . '/core/database/QueryBuilder.php';

// 硬编码加载过滤器类
// AI使用说明：Filters\Filter类提供了常用的数据验证功能
// 使用方法：
// 1. 验证ID：Filters\Filter::validateId($id)
// 2. 验证邮箱：Filters\Filter::validateEmail($email)
// 3. 验证手机号：Filters\Filter::validatePhone($phone)
// 4. 验证字符串长度：Filters\Filter::validateStringLength($str, $min, $max)
// 5. 验证URL：Filters\Filter::validateUrl($url)
// 6. 验证IP地址：Filters\Filter::validateIp($ip)
// 7. 验证日期：Filters\Filter::validateDate($date, $format)
require_once ROOT_PATH . '/core/filters/Filter.php';

// 硬编码加载安全类
require_once ROOT_PATH . '/core/security/CSRF.php';

// 加载路由类
require_once ROOT_PATH . '/core/routes/RouteRegistrar.php';
require_once ROOT_PATH . '/core/routes/RouteResolver.php';

// 加载路由配置
require_once ROOT_PATH . '/core/routes/web.php';

// 加载数据库配置
require_once ROOT_PATH . '/core/database/config.php';