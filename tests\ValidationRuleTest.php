<?php

declare(strict_types=1);

/**
 * 验证规则测试脚本
 * 
 * 测试test.php中使用的验证规则是否正常工作
 */

// 包含必要的文件
require_once __DIR__ . '/../core/own-library/autoloader/autoloader.php';

// 注册自动加载器
use Core\OwnLibrary\Autoloader\Autoloader;

$autoloader = new Autoloader();
$autoloader->addNamespace('Core\OwnLibrary', __DIR__ . '/../core/own-library');
$autoloader->register();

// 使用命名空间
use Core\OwnLibrary\Validation\ValidationHelper;

// 测试长度验证
function testLengthValidation() {
    echo "测试长度验证...\n";
    
    // 测试长度正确的值
    $params = ['id' => '12345'];
    $result = ValidationHelper::validate($params, 'id', ['长度5']);
    echo "长度正确的值: " . (empty($result) ? "通过" : "失败 - " . ($result['id'] ?? '')) . "\n";
    
    // 测试长度错误的值
    $params = ['id' => '123'];
    $result = ValidationHelper::validate($params, 'id', ['长度5']);
    echo "长度错误的值: " . (!empty($result) ? "正确捕获错误 - " . ($result['id'] ?? '') : "失败 - 应该捕获错误") . "\n";
}

// 测试最小值验证
function testMinValueValidation() {
    echo "\n测试最小值验证...\n";
    
    // 测试大于最小值的值
    $params = ['id' => '15000'];
    $result = ValidationHelper::validate($params, 'id', ['最小值10000']);
    echo "大于最小值的值: " . (empty($result) ? "通过" : "失败 - " . ($result['id'] ?? '')) . "\n";
    
    // 测试小于最小值的值
    $params = ['id' => '5000'];
    $result = ValidationHelper::validate($params, 'id', ['最小值10000']);
    echo "小于最小值的值: " . (!empty($result) ? "正确捕获错误 - " . ($result['id'] ?? '') : "失败 - 应该捕获错误") . "\n";
}

// 测试自定义错误信息
function testCustomErrorMessage() {
    echo "\n测试自定义错误信息...\n";
    
    // 测试长度验证的自定义错误信息
    $params = ['id' => '123'];
    $result = ValidationHelper::validate($params, 'id', ['长度5' => '长度必须为5个字符']);
    echo "长度验证自定义错误信息: " . (isset($result['id']) && $result['id'] === '长度必须为5个字符' ? "通过" : "失败") . "\n";
    
    // 测试最小值验证的自定义错误信息
    $params = ['id' => '5000'];
    $result = ValidationHelper::validate($params, 'id', ['最小值10000' => '值不能小于10000']);
    echo "最小值验证自定义错误信息: " . (isset($result['id']) && $result['id'] === '值不能小于10000' ? "通过" : "失败") . "\n";
}

// 执行测试
testLengthValidation();
testMinValueValidation();
testCustomErrorMessage();

echo "\n所有测试完成!\n";