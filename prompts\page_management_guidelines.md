# AiPHP 页面管理提示词

## 概述
本提示词定义了在AiPHP框架中进行页面管理的操作规范，包括创建页面、删除页面等功能。每个功能都有对应的编号，便于参考和使用。

## 1. 创建页面

### 1.1 核心规范
1. 根据要求创建页面，如果是中文的自动翻译成英文名字，比如创建用户页面，创建的名字就是user.php
2. 创建的目录是在handler\pages文件夹下。如果是创建单独页面，路径为handler\pages\页面名.php；如果是创建在某个模块下的页面，路径为handler\pages\模块名\页面名.php，例如在home下创建关于页，路径为handler\pages\home\about.php
3. 文件和文件夹的命名规则是小驼峰命名法
4. 创建页面，也要创建对应的静态资源，也就是css和js。目录结构、名字和页面一致
5. 如果指定布局文件，则用指定的布局文件，如果没有则不使用布局文件
6. 页面的结构，上面为php，下面是html代码。如果有布局，则只构建html片段，如果不用布局，则是完整的html结构
7. 为了方便测试，添加页面也要在config\routes.php路由里配置
8. 如果使用了布局文件，要在导航里添加超链接菜单

### 1.2 详细规则

#### 命名转换规则
- 中文名称需要翻译成英文名称
- 文件名和文件夹名都使用小驼峰命名法 (camelCase)
- 示例：
  - "用户页面" → user.php
  - "产品列表" → productList.php
  - "订单详情" → orderDetail.php
  - "用户管理" → userManagement
  - "商品分类" → productCategory

#### 文件路径规范
- 单独页面文件路径：handler/pages/[页面名].php
- 模块下页面文件路径：handler/pages/[模块名]/[页面名].php
- CSS文件路径：public/static/own/css/[模块路径]/[页面名].css
- JS文件路径：public/static/own/js/[模块路径]/[页面名].js

重要说明：
- 静态资源文件（CSS和JS）的文件名应与对应的页面文件名保持一致
- 如果是单独页面（如about.php），对应的静态资源文件路径为public/static/own/css/about.css和public/static/own/js/about.js
- 如果是模块下的页面（如home/about.php），对应的静态资源文件路径为public/static/own/css/home/<USER>/static/own/js/home/<USER>
- 静态资源目录结构应与页面目录结构完全一致

#### 页面结构规范

##### 使用布局的页面结构
```php
<?php
// 页面描述注释

// 设置页面特定变量
$pageTitle = '页面标题 - AiPHP应用';
$pageDescription = '页面描述';
$pageKeywords = '关键词1, 关键词2';

// 如果使用布局，需要引入对应的CSS和JS文件
$additionalCSS = ['/static/own/css/目录名/文件名.css'];
$additionalJS = ['/static/own/js/目录名/文件名.js'];

// 页面内容
ob_start();
?>
<div class="page-class">
    <!-- 页面HTML内容 -->
</div>
<?php
$content = ob_get_clean();

// 载入布局文件
require LAYOUTS_PATH . '/layout_file.php';
?>
```

##### 不使用布局的页面结构
```php
<?php
// 页面描述注释

// 设置页面特定变量
$pageTitle = '页面标题 - AiPHP应用';
$pageDescription = '页面描述';
$pageKeywords = '关键词1, 关键词2';

// 页面内容
ob_start();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($pageTitle); ?></title>
    <meta name="description" content="<?php echo htmlspecialchars($pageDescription); ?>">
    <meta name="keywords" content="<?php echo htmlspecialchars($pageKeywords); ?>">
    <link rel="stylesheet" href="/static/own/css/main.css">
    <!-- 如果不使用布局，在HTML头部添加页面特定CSS -->
    <link rel="stylesheet" href="/static/own/css/目录名/文件名.css">
</head>
<body>
    <!-- 页面内容 -->
    <div class="page-class">
        <!-- 页面HTML内容 -->
    </div>
    
    <script src="/static/own/js/main.js"></script>
    <!-- 如果不使用布局，在body底部添加页面特定JS -->
    <script src="/static/own/js/目录名/文件名.js"></script>
</body>
</html>
<?php
ob_end_flush();
?>
```

#### 路由配置规范
- 所有新创建的页面都需要在config/routes.php中配置路由
- 路由配置遵循现有格式：
  ```php
  // GET请求路由
  'GET' => [
      // ...
      '/路径' => '页面路径（相对于handler/pages）',
  ],
  ```
- 路由参数写法：使用花括号{}包含参数名
  ```php
  // 带参数的路由示例
  '/users/{id}' => 'users/view',
  '/posts/{id}/edit' => 'posts/edit',
  '/products/category/{id}' => 'products/category',
  ```
- 常见参数类型：
  - `{id}` - 数字ID参数
  - `{slug}` - 字符串标识参数
- 不同HTTP方法的路由配置：
  ```php
  // GET请求路由
  'GET' => [
      '/路径' => '页面路径',
  ],
  // POST请求路由
  'POST' => [
      '/路径' => '页面路径',
  ],
  // PUT请求路由
  'PUT' => [
      '/路径' => '页面路径',
  ],
  // DELETE请求路由
  'DELETE' => [
      '/路径' => '页面路径',
  ],
  ```
- 示例：创建了handler/pages/testPage/view.php页面，需要添加：
  ```php
  '/test-page/view/{id}' => 'testPage/view',
  ```
- 示例：创建了handler/pages/about.php页面，需要添加：
  ```php
  '/about' => 'about',
  ```

#### 导航菜单更新规范
- 如果页面使用布局文件，则需要在布局文件的导航菜单中添加链接
- 导航菜单位于布局文件的`<nav class="site-nav">`部分
- 链接格式：
  ```html
  <li><a href="/路径">页面名称</a></li>
  ```
- 示例：为测试页面添加导航链接：
  ```html
  <li><a href="/test-page">测试页面</a></li>
  ```
- 注意：带参数的路由通常不需要添加到导航菜单中

#### 静态资源创建规范
- 每个页面都需要创建对应的CSS和JS文件
- CSS和JS文件名与页面文件名保持一致
- 静态资源目录结构与页面目录结构一致
- 静态资源目录位于public/static/own下

#### 布局文件使用规范
- 如果指定布局文件，则使用指定的布局文件
- 如果未指定布局文件，则创建完整的HTML页面结构
- 常用布局文件位于handler/layouts/目录下

### 1.3 示例

#### 创建"关于我们"页面（单独页面示例）
1. 中文名称：关于我们
2. 英文翻译：about
3. 页面路径：handler/pages/about.php
4. CSS路径：public/static/own/css/about.css
5. JS路径：public/static/own/js/about.js
6. 路由配置：在config/routes.php中添加
   ```php
   '/about' => 'about',
   ```
7. 导航菜单：在布局文件中添加
   ```html
   <li><a href="/about">关于我们</a></li>
   ```

#### 创建"联系我们"页面（单独页面示例）
1. 中文名称：联系我们
2. 英文翻译：contact
3. 页面路径：handler/pages/contact.php
4. CSS路径：public/static/own/css/contact.css
5. JS路径：public/static/own/js/contact.js
6. 路由配置：在config/routes.php中添加
   ```php
   '/contact' => 'contact',
   ```
7. 导航菜单：在布局文件中添加
   ```html
   <li><a href="/contact">联系我们</a></li>
   ```

#### 创建"用户设置"页面（模块下页面示例）
1. 中文名称：用户设置
2. 英文翻译：user/settings
3. 页面路径：handler/pages/user/settings.php
4. CSS路径：public/static/own/css/user/settings.css
5. JS路径：public/static/own/js/user/settings.js
6. 路由配置：在config/routes.php中添加
   ```php
   '/user/settings' => 'user/settings',
   ```
7. 导航菜单：在布局文件中添加
   ```html
   <li><a href="/user/settings">用户设置</a></li>
   ```

### 1.4 最佳实践
1. 始终使用输出缓冲捕获页面内容
2. 使用适当的HTML实体转义函数防止XSS攻击
3. 为每个页面设置合适的meta信息
4. 保持CSS和JS文件与页面功能一致
5. 遵循框架的目录结构和命名规范
6. 在创建页面前确认是否需要使用布局文件
7. 创建页面后及时更新路由配置以便测试
8. 若使用布局文件，记得更新导航菜单以方便访问
9. 带参数的路由应根据实际需求选择合适的参数类型（id或slug）

## 2. 删除页面

### 2.1 核心规范
1. 从路由配置中删除对应的路由条目
2. 删除页面文件或整个页面目录
3. 删除页面对应的静态资源文件（CSS和JS）
4. 如果页面在布局文件中有对应的导航菜单项，需要一并删除

### 2.2 详细规则

#### 删除路由配置
- 在config/routes.php文件中删除不再需要的路由条目
- 确保删除所有与该页面相关的GET、POST、PUT、DELETE等请求方法的路由

#### 删除页面文件
- 删除handler/pages目录下对应的页面文件或目录
- 如果页面目录中只包含一个页面文件，则删除整个目录
- 如果页面目录中包含多个相关页面文件，只删除不需要的页面文件

#### 删除静态资源
- 删除public/static/own/css目录下对应的CSS文件
- 删除public/static/own/js目录下对应的JS文件
- 注意检查是否有其他页面共享这些静态资源，避免误删仍在使用的资源

#### 删除布局中的导航菜单项
- 检查handler/layouts目录下的布局文件，查找与被删除页面相关的导航菜单项
- 删除布局文件中对应的导航链接
- 确保删除的导航链接与被删除页面的路由路径相匹配
- 注意检查所有布局文件，确保没有遗漏

#### 删除确认检查
- 确认路由配置中已无相关路由条目
- 确认页面文件已删除
- 确认静态资源文件已删除
- 确认布局文件中的导航菜单项已删除
- 测试应用确保删除操作未影响其他功能