<?php

declare(strict_types=1);

/**
 * EnhancedValidationHelper综合测试类
 * 
 * 全面测试ValidationHelper是否支持EnhancedParameterValidator的所有验证方法
 */

// 包含必要的文件
require_once __DIR__ . '/../core/own-library/autoloader/autoloader.php';

// 注册自动加载器
use Core\OwnLibrary\Autoloader\Autoloader;

$autoloader = new Autoloader();
$autoloader->addNamespace('Core\OwnLibrary', __DIR__ . '/../core/own-library');
$autoloader->register();

// 使用命名空间
use Core\OwnLibrary\Validation\ValidationHelper;

// 测试统计变量
$tests = 0;
$passed = 0;
$failed = 0;

// 测试函数定义
function runTest($name, $testFunction) {
    global $tests, $passed, $failed;
    
    $tests++;
    try {
        $result = $testFunction();
        if ($result) {
            $passed++;
            echo "✓ $name\n";
        } else {
            $failed++;
            echo "✗ $name\n";
        }
    } catch (Exception $e) {
        $failed++;
        echo "✗ $name - Exception: " . $e->getMessage() . "\n";
    }
}

// 测试必填验证
function testRequiredValidation() {
    $params = [];
    $result = ValidationHelper::validate($params, 'id', ['必填' => 'ID必须填写']);
    return isset($result['id']) && $result['id'] === 'ID必须填写';
}

// 测试类型验证 - 数字
function testTypeIntValidation() {
    $params = ['id' => '123'];
    $result = ValidationHelper::validate($params, 'id', ['数字']);
    return empty($result);
}

// 测试类型验证 - 整数
function testTypeIntegerValidation() {
    $params = ['id' => 123];
    $result = ValidationHelper::validate($params, 'id', ['整数']);
    return empty($result);
}

// 测试类型验证 - 字符串
function testTypeStringValidation() {
    $params = ['name' => 'test'];
    $result = ValidationHelper::validate($params, 'name', ['字符串']);
    return empty($result);
}

// 测试类型验证 - 布尔值
function testTypeBooleanValidation() {
    $params = ['active' => true];
    $result = ValidationHelper::validate($params, 'active', ['布尔值']);
    return empty($result);
}

// 测试类型验证 - 浮点数
function testTypeFloatValidation() {
    $params = ['price' => 99.99];
    $result = ValidationHelper::validate($params, 'price', ['浮点数']);
    return empty($result);
}

// 测试类型验证 - 数组
function testTypeArrayValidation() {
    $params = ['tags' => ['php', 'javascript']];
    $result = ValidationHelper::validate($params, 'tags', ['数组']);
    return empty($result);
}

// 测试类型验证 - 空值
function testTypeNullValidation() {
    $params = ['optional' => null];
    $result = ValidationHelper::validate($params, 'optional', ['空值']);
    return empty($result);
}

// 测试格式验证 - 邮箱
function testFormatEmailValidation() {
    $params = ['email' => '<EMAIL>'];
    $result = ValidationHelper::validate($params, 'email', ['邮箱']);
    return empty($result);
}

// 测试格式验证 - 网址
function testFormatUrlValidation() {
    $params = ['website' => 'https://example.com'];
    $result = ValidationHelper::validate($params, 'website', ['网址']);
    return empty($result);
}

// 测试格式验证 - IP地址
function testFormatIpValidation() {
    $params = ['ip' => '***********'];
    $result = ValidationHelper::validate($params, 'ip', ['IP地址']);
    return empty($result);
}

// 测试格式验证 - IPv4
function testFormatIPv4Validation() {
    $params = ['ip' => '***********'];
    $result = ValidationHelper::validate($params, 'ip', ['IPv4']);
    return empty($result);
}

// 测试格式验证 - IPv6
function testFormatIPv6Validation() {
    $params = ['ip' => '2001:0db8:85a3:0000:0000:8a2e:0370:7334'];
    $result = ValidationHelper::validate($params, 'ip', ['IPv6']);
    return empty($result);
}

// 测试格式验证 - 字母
function testFormatAlphaValidation() {
    $params = ['code' => 'ABC'];
    $result = ValidationHelper::validate($params, 'code', ['字母']);
    return empty($result);
}

// 测试格式验证 - 字母数字
function testFormatAlnumValidation() {
    $params = ['code' => 'ABC123'];
    $result = ValidationHelper::validate($params, 'code', ['字母数字']);
    return empty($result);
}

// 测试格式验证 - 数值
function testFormatNumericValidation() {
    $params = ['amount' => '123.45'];
    $result = ValidationHelper::validate($params, 'amount', ['数值']);
    return empty($result);
}

// 测试格式验证 - 数字串
function testFormatDigitsValidation() {
    $params = ['pin' => '123456'];
    $result = ValidationHelper::validate($params, 'pin', ['数字串']);
    return empty($result);
}

// 测试格式验证 - slug
function testFormatSlugValidation() {
    $params = ['slug' => 'test-post-123'];
    $result = ValidationHelper::validate($params, 'slug', ['slug']);
    return empty($result);
}

// 测试格式验证 - 日期
function testFormatDateValidation() {
    $params = ['date' => '2023-01-01'];
    $result = ValidationHelper::validate($params, 'date', ['日期']);
    return empty($result);
}

// 测试格式验证 - 日期时间
function testFormatDateTimeValidation() {
    $params = ['datetime' => '2023-01-01 12:00:00'];
    $result = ValidationHelper::validate($params, 'datetime', ['日期时间']);
    return empty($result);
}

// 测试格式验证 - UUID
function testFormatUUIDValidation() {
    $params = ['uuid' => '550e8400-e29b-41d4-a716-************'];
    $result = ValidationHelper::validate($params, 'uuid', ['UUID']);
    return empty($result);
}

// 测试格式验证 - 信用卡
function testFormatCreditCardValidation() {
    $params = ['card' => '****************'];
    $result = ValidationHelper::validate($params, 'card', ['信用卡']);
    return empty($result);
}

// 测试格式验证 - 手机号
function testFormatPhoneValidation() {
    $params = ['phone' => '13812345678'];
    $result = ValidationHelper::validate($params, 'phone', ['手机']);
    return empty($result);
}

// 测试格式验证 - 邮政编码
function testFormatPostalCodeValidation() {
    $params = ['zip' => '123456'];
    $result = ValidationHelper::validate($params, 'zip', ['邮政编码']);
    return empty($result);
}

// 测试长度验证 - 精确长度
function testLengthExactValidation() {
    $params = ['code' => '123456'];
    $result = ValidationHelper::validate($params, 'code', ['长度6']);
    return empty($result);
}

// 测试长度验证 - 范围长度
function testLengthRangeValidation() {
    $params = ['password' => '12345678'];
    $result = ValidationHelper::validate($params, 'password', ['长度6-12']);
    return empty($result);
}

// 测试最小长度验证
function testMinLengthValidation() {
    $params = ['password' => '123456'];
    $result = ValidationHelper::validate($params, 'password', ['最小长度6']);
    return empty($result);
}

// 测试最大长度验证
function testMaxLengthValidation() {
    $params = ['name' => 'test'];
    $result = ValidationHelper::validate($params, 'name', ['最大长度10']);
    return empty($result);
}

// 测试范围验证 - 整数范围
function testRangeIntValidation() {
    $params = ['age' => '25'];
    $result = ValidationHelper::validate($params, 'age', ['范围18-65']);
    return empty($result);
}

// 测试范围验证 - 浮点数范围
function testRangeFloatValidation() {
    $params = ['price' => '99.99'];
    $result = ValidationHelper::validate($params, 'price', ['范围0.01-999.99']);
    return empty($result);
}

// 测试最小值验证
function testMinValueValidation() {
    $params = ['score' => '60'];
    $result = ValidationHelper::validate($params, 'score', ['最小值0']);
    return empty($result);
}

// 测试最大值验证
function testMaxValueValidation() {
    $params = ['score' => '100'];
    $result = ValidationHelper::validate($params, 'score', ['最大值100']);
    return empty($result);
}

// 测试枚举验证
function testEnumValidation() {
    $params = ['status' => 'active'];
    $result = ValidationHelper::validate($params, 'status', ['枚举(active,inactive,pending)']);
    return empty($result);
}

// 测试正则表达式验证
function testRegexValidation() {
    $params = ['username' => 'test123'];
    $result = ValidationHelper::validate($params, 'username', ['正则(/^[a-zA-Z0-9]+$/)' ]);
    return empty($result);
}

// 测试自定义错误信息
function testCustomErrorMessage() {
    $params = [];
    $result = ValidationHelper::validate($params, 'id', ['必填' => '自定义错误信息']);
    return isset($result['id']) && $result['id'] === '自定义错误信息';
}

// 执行测试
echo "开始测试ValidationHelper类对EnhancedParameterValidator所有方法的支持...\n\n";

runTest("必填验证", "testRequiredValidation");
runTest("类型验证 - 数字", "testTypeIntValidation");
runTest("类型验证 - 整数", "testTypeIntegerValidation");
runTest("类型验证 - 字符串", "testTypeStringValidation");
runTest("类型验证 - 布尔值", "testTypeBooleanValidation");
runTest("类型验证 - 浮点数", "testTypeFloatValidation");
runTest("类型验证 - 数组", "testTypeArrayValidation");
runTest("类型验证 - 空值", "testTypeNullValidation");
runTest("格式验证 - 邮箱", "testFormatEmailValidation");
runTest("格式验证 - 网址", "testFormatUrlValidation");
runTest("格式验证 - IP地址", "testFormatIpValidation");
runTest("格式验证 - IPv4", "testFormatIPv4Validation");
runTest("格式验证 - IPv6", "testFormatIPv6Validation");
runTest("格式验证 - 字母", "testFormatAlphaValidation");
runTest("格式验证 - 字母数字", "testFormatAlnumValidation");
runTest("格式验证 - 数值", "testFormatNumericValidation");
runTest("格式验证 - 数字串", "testFormatDigitsValidation");
runTest("格式验证 - slug", "testFormatSlugValidation");
runTest("格式验证 - 日期", "testFormatDateValidation");
runTest("格式验证 - 日期时间", "testFormatDateTimeValidation");
runTest("格式验证 - UUID", "testFormatUUIDValidation");
runTest("格式验证 - 信用卡", "testFormatCreditCardValidation");
runTest("格式验证 - 手机号", "testFormatPhoneValidation");
runTest("格式验证 - 邮政编码", "testFormatPostalCodeValidation");
runTest("长度验证 - 精确长度", "testLengthExactValidation");
runTest("长度验证 - 范围长度", "testLengthRangeValidation");
runTest("最小长度验证", "testMinLengthValidation");
runTest("最大长度验证", "testMaxLengthValidation");
runTest("范围验证 - 整数范围", "testRangeIntValidation");
runTest("范围验证 - 浮点数范围", "testRangeFloatValidation");
runTest("最小值验证", "testMinValueValidation");
runTest("最大值验证", "testMaxValueValidation");
runTest("枚举验证", "testEnumValidation");
runTest("正则表达式验证", "testRegexValidation");
runTest("自定义错误信息", "testCustomErrorMessage");

// 输出测试结果统计
echo "\n测试完成!\n";
echo "总测试数: $tests\n";
echo "通过: $passed\n";
echo "失败: $failed\n";
echo "通过率: " . ($tests > 0 ? round(($passed / $tests) * 100, 2) : 0) . "%\n";